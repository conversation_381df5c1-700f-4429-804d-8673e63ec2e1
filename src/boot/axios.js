// import something here
import axios from 'axios'

console.log("base",process.env.BASE_URL)
const Api = axios.create({
  baseURL: process.env.BASE_URL

})

if (process.env.NODE_ENV === "development") {
  Api.interceptors.response.use(
    (res) => res,
    (err) => {
      if (err.response.status === 404) {
        throw new Error(`${err.config.url} not found`);
      }
      throw err;
    }
  );

  Api.interceptors.request.use((req) => {
    console.log(`${req.method} ${req.url}`);
    return req;
  });
}


// "async" is optional
export default async ({ Vue, store, router ,urlPath}) => {

  if (store.state.auth.token) {
    Api.defaults.headers.common["Authorization"] = `Bearer ${store.state.auth.token}`;
  }

  if (process.env.NODE_ENV === "development") {
    Api.interceptors.response.use(
      (res) => res,
      (err) => {
        if (err.response.status === 404) {
          throw new Error(`${err.config.url} not found`);
        }
        throw err;
      }
    );

    Api.interceptors.request.use((req) => {
      console.log(`${req.method} ${req.url}`);
      return req;
    });
  }

  Api.interceptors.response.use(
    (res) => res,
    (error) => {
      if (error.response && error.response.status === 401 && urlPath != '/') {
        store.dispatch(`auth/logout`).then(() => {
          router.push({
            name: "login"
          })
        }).catch(err => {
          throw new Error(`${err}`);
        });
      }
      return Promise.reject(error.response.data);
    })

  // something to do
  Vue.prototype.$axios = Api
}

export {Api}
