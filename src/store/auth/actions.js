import AuthService from 'src/services/auth'
import {Api} from 'boot/axios'

export function login ({commit},payload) {
    return new Promise((resolve,reject) => {
        AuthService.LOGIN(payload).then(response => {
            const {result,status} = response.data;
            commit('setToken', result.claim_token)
            localStorage.setItem('token',result.claim_token)
            Api.defaults.headers.common['Authorization'] = result.claim_token 
            const user = {
                email: result.email,
                id: result.id,
                last_name: result.last_name,
                first_name: result.first_name,
                role: result.role,
                role_id: result.role_id
            }
            commit('setUser', user)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })
}

export function resetPassword({}, payload) {
    return new Promise((resolve,reject) => {
        AuthService.RESET_PASSWORD(payload).then(response => {
            const {status} = response.data
            resolve(status)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })  
}

export function changePassword({}, payload) {
    return new Promise((resolve,reject) => {
        AuthService.CHANGE_PASS(payload).then(response => {
            const {result} = response.data
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    }) 
}

export function logout ({commit}) {
    return new Promise((resolve,reject) => {
        commit('logout')
        localStorage.removeItem('token')
        delete Api.defaults.headers.common['Authorization']
        resolve(true)
      })
      .catch(err => {
        // commit('some error')
        console.log(err.message)
        reject(err)
    })
}
