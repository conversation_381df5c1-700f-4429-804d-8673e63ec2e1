import Vue from 'vue'
import Vuex from 'vuex'
import auth from "./auth"
import cases from "./cases"
import admin from "./admin"
import events from './events'
import VuexPersist from 'vuex-persist';


Vue.use(Vuex)
let store = null;

const vuexLocalStorage = new VuexPersist({
  storage: window.localStorage, // or window.sessionStorage or localForage instance.
  // Function that passes the state and returns the state with only the objects you want to store.
  reducer: state => ({
    auth: state.auth,
    // getRidOfThisModule: state.getRidOfThisModule (No one likes it.)
  })
})


export default function (/* { ssrContext } */) {
    const Store = new Vuex.Store({
      modules: {
        auth,
        cases,
        admin,
        events
      },
      plugins: [vuexLocalStorage.plugin],
  
      // enable strict mode (adds overhead!)
      // for dev mode only
      strict: process.env.NODE_ENV !== 'production'
    })
  
    // export store for other files that need it
    store = Store;
  
    return Store
  }
  
  export {store};
  