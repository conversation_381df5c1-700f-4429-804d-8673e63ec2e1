import AdminService from 'src/services/admin'
import { setPensionersExportExcel} from "src/store/events/mutations";
import { setSmpExportExcel} from "src/store/events/mutations";
import {setMoneyTransfersExportExcel} from "src/store/events/mutations";

export function getAllSmp({ commit }, { page = 1, per_page = 100, sort_field = "oper_date_time", sort_order = "asc", filter_fields = "", filter_values = "", start_date = "", end_date = "" }) {
    return new Promise((resolve, reject) => {
        AdminService.GET_ALL_SMP(page, per_page, sort_field, sort_order, filter_fields, filter_values, start_date, end_date).then(response => {
            const { result } = response.data
            commit('setSmp', result.items || [])
            resolve(result.count)
        })
            .catch(err => {
                console.log(err.message)
                reject(err)
            })
    })
}

export function getSmpExportExcel({ commit }, { start_date, end_date }) {
  return new Promise((resolve, reject) => {
    AdminService.GET_SMP_EXPORT_EXCEL(start_date, end_date)
      .then(response => {
        if (response.status === 200) {
          commit("setSmpExportExcel", response.data);
          resolve(response);
        } else {
          reject(new Error("Ошибка при скачивании файла"));
        }
      })
      .catch(err => {
        console.log(err.message);
        reject(err);
      });
  });
}

export function getAllMoneyTransfers({ commit }, { page = 1, per_page = 100, sort_field = "oper_date_time", sort_order = "asc", filter_fields = "", filter_values = "", start_date = "", end_date = "" }) {
    return new Promise((resolve, reject) => {
        AdminService.GET_ALL_MONEY_TRANSFERS(page, per_page, sort_field, sort_order, filter_fields, filter_values, start_date, end_date).then(response => {
            const { result } = response.data
            commit('setMoneyTransfers', result.items || [])
            resolve(result.count)
        })
            .catch(err => {
                console.log(err.message)
                reject(err)
            })
    })
}

export function getMoneyTransfersExportExcel({ commit }, { start_date = "", end_date = "" }) {
  return new Promise((resolve, reject) => {
    AdminService.GET_MONEY_TRANSFERS_EXPORT_EXCEL(start_date, end_date)
      .then(response => {
        if (response.status === 200) {
          commit("setMoneyTransfersExportExcel", response.data);
          resolve(response);
        } else {
          reject(new Error("Ошибка при скачивании файла"));
        }
      })
      .catch(err => {
        console.log(err.message);
        reject(err);
      });
  });
}



export function getAllPensionersTransfers({ commit }, { page = 1, per_page = 100, sort_field = "oper_date_time", sort_order = "asc", filter_fields = "", filter_values = "", start_date = "", end_date = "" }) {
    return new Promise((resolve, reject) => {
        AdminService.GET_ALL_PENSIONERS_TRANSFERS(page, per_page, sort_field, sort_order, filter_fields, filter_values, start_date, end_date).then(response => {
            const { result } = response.data
            commit('setPensionersTransfers', result.items || [])
            resolve(result.count)
        })
            .catch(err => {
                console.log(err.message)
                reject(err)
            })
    })
}

export function getPensionerExportExcel({ commit }, { start_date = "", end_date = "" }) {
  return new Promise((resolve, reject) => {
    AdminService.GET_PENSIONER_EXPORT_EXCEL(start_date, end_date)
      .then(response => {
        if (response.status === 200) {
          commit("setPensionersExportExcel", response.data);
          resolve(response);
        } else {
          reject(new Error("Ошибка при скачивании файла"));
        }
      })
      .catch(err => {
        console.log(err.message);
        reject(err);
      });
  });
}
