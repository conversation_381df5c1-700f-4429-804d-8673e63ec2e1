import AdminService from 'src/services/admin'
import AuthService from 'src/services/auth'

export function addEmailNotification({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_EMAIL_NOTIFICATIONS(payload).then(response => {
            const {result, status} = response.data;
            commit('addEmailNotification', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editEmailNotification({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_EMAIL_NOTIFICATIONS(payload).then(response => {
            const {result, status} = response.data;
            commit('editEmailNotification', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function getEmailNotifications({commit}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_EMAIL_LIST_NOTIFICATIONS().then(response => {
            const {result} = response.data;
            commit('setEmailNotifications', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function deleteEmailNotification({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_EMAIL_NOTIFICATION_BY_ID(id).then(response => {
            const {result} = response.data;
            commit('deleteEmailNotification', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function getWay4CardOperationFrequency ({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_WAY4_CARD_OPERATION_FREQUENCY_REPORT(start_date, end_date).then(response => {
            const {result} = response.data;
            commit('setWay4CardOperationFrequency', result || [])
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getWay4CardFrequency ({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_WAY4_CARD_FREQUENCY_REPORT(start_date, end_date).then(response => {
            const {result} = response.data;
            commit('setWay4CardFrequency', result || [])
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getWay4AllTransactions ({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_WAY4_ALL_TRANSACTIONS(start_date, end_date).then(response => {
            const {result} = response.data;
            commit('setWay4AllTransactions', result['Body']['GET_TRN_LIST_BY_DATEResponse']['result'] || [])
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getReport1({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT1(start_date, end_date).then(response => {
            const {result} = response.data;
            commit('setReport1', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport2({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT2(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport2', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport3({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT3(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport3', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport4({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT4(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport4', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport5({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT5(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport5', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport6({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT6(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport6', result || {})
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport7({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT7(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport7', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport8({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT8(start_date, end_date).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setReport8', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport9({commit}, {start_date, end_date}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT9(start_date, end_date).then(response => {
            const {result} = response.data;
            commit('setReport9', result)
            resolve(result.length)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getReport10({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values="",start_date="",end_date=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_REPORT10(page,per_page,sort_field, sort_order, filter_fields, filter_values, start_date, end_date).then(response => {
            const {result} = response.data;
            console.log(result)
            commit('setReport10', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editIntegrationList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_INTEGRATION_LIST(payload).then(response => {
            commit('editIntegrationList', payload)
            resolve(payload)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}


export function getDjList({commit}, {page=1, per_page=100, sort_field, sort_order}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_DJ_LIST(page,per_page,sort_field, sort_order).then(response => {
            console.log(response.data)
            const {result} = response.data;
            commit('setDjList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getIntegrationList ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_INTEGRATION_LIST(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setIntegrationList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getEventTypeList ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_EVENT_TYPES_LIST(page,per_page).then(response => {
            const {result} = response.data;
            commit('setEventTypeList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getHistoryStorageList ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_HISTORYSTORAGE_SETTINGS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setHistoryStorageSettings', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function updateHistoryStorageSettings ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPATE_HISTORYSTORAGE_SETTINGS(payload).then(response => {
            const {result} = response.data;
            commit('updateHistoryStorageSettings', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function getSystemList ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_SYSTEM_LIST(page,per_page).then(response => {
            const {result} = response.data;
            commit('setSystemList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getBlackListCards ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_BLACK_LIST_CARDS(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setBlackListCards', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getScoringList ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_SCORING_LIST(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setScoringList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function addBlackListCard ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_BLACK_LIST_CARD(payload).then(response => {
            const {result,status} = response.data;
            commit('addBlackListCard', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function addScoringListItem ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_SCORING_ITEM(payload).then(response => {
            const {result,status} = response.data;
            commit('addScoringListItem', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editBlackListCard ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_BLACK_LIST_CARD(payload).then(response => {
            const {result} = response.data;
            commit('editBlackListCard', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editScoringListItem ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_SCORING_ITEM(payload).then(response => {
            const {result} = response.data;
            commit('editScoringListItem', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function deleteBlackListCard ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_BLACK_LIST_CARD(id).then(response => {
            const {result} = response.data;
            commit('deleteBlackListCard', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function deleteScoringListItem ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_SCORING_ITEM(id).then(response => {
            const {result} = response.data;
            commit('deleteScoringListItem', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getTransferLimitParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TRANSFER_LIMIT_SERVICE_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setTransferLimitParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editTransferLimitParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_TRANSFER_LIMIT_SERVICE_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editTransferLimitParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getNewPayeeParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_NEW_PAYEE(page,per_page).then(response => {
            const {result} = response.data;
            commit('setNewPayeeParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editNewPayeeParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_NEW_PAYEE(payload).then(response => {
            const {result} = response.data;
            commit('editNewPayeeParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getPaymentProfileParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_PAYMENT_PROFILE_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setPaymentProfileParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editPaymentProfileParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_PAYMENT_PROFILE_PARAMS(payload).then(response => {
            const {result} = response.data;
            console.log(result)
            commit('editPaymentProfileParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function getInactiveAccParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_INACTIVE_ACC_PARAMAS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setInactiveAccParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editInactiveAccParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_INACTIVE_ACC_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editInactiveAccParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}
export function editPaymentTrendParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_PAYMENT_TREND_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editPaymentTrendParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}



export function getPaymentTrendParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_PAYMENT_TREND_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setPaymentTrendParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editAgeCheckerParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_AGE_CHECKER_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editAgeCheckerParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getAgeCheckerParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_AGE_CHECKER_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setAgeCheckerParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editNewCardTransactionParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_NEW_CARD_TRANSACTION_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editNewCardTransactionParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getNewCardTransactionParams ({commit}, {page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_NEW_CARD_TRANSACTION_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setNewCardTransactionParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}
export function getHdbkProducts ({commit},{page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_HDBK_PRODUCTS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setHdbkProducts', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function addFinancialSpeedPaymentParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_FINANCIAL_SPEED_PAYMENT_PARAM(payload).then(response => {
            const {result,status} = response.data;
            commit('addFinancialSpeedPaymentParam', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function addFinancialLimitParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_FINANCIAL_LIMIT_PARAM(payload).then(response => {
            const {result,status} = response.data;
            commit('addFinancialLimitParams', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function getCaseStatusesSystem({commit},{page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_CASE_STATUSES_SYSTEM(page,per_page).then(response => {
            const {result} = response.data;
            commit('setStatusesSystem', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function getEmployeesBlackList({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_EMPLOYEES_BLACKLIST(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setEmployeesBlackList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getNotTypicalAmountServiceParams ({commit}, {page=1, per_page=5}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_NOT_TYPICAL_AMOUNT_SERVICE_PARAMS(page,per_page).then(response => {
            const {result} = response.data;
            commit('setNotTypicalAmountParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getSolutionListCaseButtons ({commit},{page,per_page}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_HDBK_SOLUTION_LIST(page,per_page).then(response => {
            const {result} = response.data;
            commit('setSolutionListCaseButtons', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function updateCaseButtons ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_CASE_BUTTON(payload).then(response => {
            const {result} = response.data;
            commit('updateCaseButtons', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editNotTypicalAmountServiceParams ({commit} , payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_NOT_TYPICAL_AMOUNT_SERVICE_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editNotTypicalAmountServiceParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}


export function getNotTypicalAmountServicePercent ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_NOT_TYPYCAL_AMOUNT_SERVICE_PERCENT(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setNotTypicalAmountServicePercent', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editNotTypicalAmountServicePercent ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(payload).then(response => {
            const {result} = response.data;
            commit('editNotTypicalAmountServicePercent', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
} 

export function deleteNotTypicalAmountServicePercent ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(id).then(response => {
            const {result} = response.data;
            commit('deleteNotTypicalAmountServicePercent', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function addNotTypicalAmountServicePercent ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(payload).then(response => {
            const {result, status} = response.data;
            commit('addNotTypicalAmountServicePercent', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}


export function getFinancialLimitParams ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_FINANCIAL_LIMIT_PARAMS(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setFinancialLimitParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}


export function editFinancialSpeedPaymentParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_FINANCIAL_SPEED_PAYMENT_PARAM(payload).then(response => {
            const {result} = response.data;
            commit('editFinancialSpeedPaymentParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
} 

export function editFinancialLimitParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_FINANCIAL_LIMIT_PARAM(payload).then(response => {
            const {result} = response.data;
            commit('editFinancialLimitParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function deleteFinancialLimitParams ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_FINANCIAL_LIMIT_PARAM(id).then(response => {
            const {result} = response.data;
            commit('deleteFinancialLimitParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function deleteFinancialSpeedPaymentParam({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_FINANCIAL_SPEED_PAYMENT_PARAM(id).then(response => {
            const {result} = response.data;
            commit('deleteFinancialSpeedPaymentParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getFinancialSpeedPaymentParams ({commit},  {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_FINANCIAL_SPEED_PAYMENT_PARAMS(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setFinancialSpeedPaymentParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getMultipleLoginParams ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_MULTIPLE_LOGIN_PARAMS(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setMultipleLoginParams', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function editLoginParams ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_LOGIN_PARAMS(payload).then(response => {
            const {result} = response.data;
            commit('editLoginParams', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}


export function getCaseStatuses({commit},{page=1, per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_CASE_STATUSES(page,per_page).then(response => {
            const {result} = response.data;
            commit('setStatuses', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editParamsRule1 ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_PARAMS_BLACKLIST(payload).then(response => {
            const {result} = response.data;
            commit('editParamsRule1', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getExternalIp ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_EXTERNAL_IP(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setIpExternal', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getTerroristsSsiIndv ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_SSI_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristSsiIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsSsiLe ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_SSI_LE(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristSsiLe', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsOfacIndv ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_OFAC_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristOfacIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsOfacLe ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_OFAC_LE(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristOfacLe', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsKfmIndv ({commit}, {page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_KFM_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristKfmIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsKfmLe ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_KFM_LE(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristKfmLe', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsEsIndv ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORIST_ES_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristEsIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getRulesParam10 ({commit}, {page=1,per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_RULE_10_PARAM(page,per_page).then(response => {
            const {result} = response.data;
            commit('setRules10Param', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function editRules10Param ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_RULES10_PARAM(payload).then(response => {
            const {result} = response.data;
            commit('editRules10Param', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getRulesParam1 ({commit}, {page=1,per_page=100}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_RULE1_PARAM(page,per_page).then(response => {
            const {result} = response.data;
            commit('setRules1Param', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getTerroristsOonIndv ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORISTS_OON_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristsOonIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsOonLe ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TERRORISTS_OON_LE(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTerroristsOonLe', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}


export function getTerroristsTalibanIndv ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TALIBAN_TERRORISTS_INDV(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTalibanTerroristsIndv', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function getTerroristsTalibanLe ({commit},{page=1, per_page=100,sort_field="", sort_order="",filter_fields="",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_TALIBAN_TERRORISTS_LE(page,per_page,sort_field, sort_order, filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setTalibanTerroristsLe', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })    
}

export function editPhoneBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_PHONE_BLACKLIST(payload).then(response => {
            const {result} = response.data;
            commit('editPhoneBlackList', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editIpBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_IP_BLACKLIST(payload).then(response => {
            const {result} = response.data;
            commit('editIpBlackList', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editMacBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_MAC_BLACKLIST(payload).then(response => {
            const {result} = response.data;
            commit('editMacBlackList', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editClientBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_CLIENT_BLACKLIST(payload).then(response => {
            const {result} = response.data;
            commit('editClientBlackList', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function editClientWhiteList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_CLIENT_WHITELIST(payload).then(response => {
            const {result} = response.data;
            commit('editClientWhiteList', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}


export function addPhoneBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_PHONE_BLACKLIST(payload).then(response => {
            const {result, status} = response.data;
            commit('addPhoneBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function deletePhoneBlackList ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_PHONE_BLACKLIST(id).then(response => {
            const {result, status} = response.data;
            commit('deletePhoneBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getPhonesBlackList ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_PHONES_BLACKLIST(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result} = response.data;
            commit('setPhoneList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getIpBlackList ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_IP_BLACKLIST(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result, status} = response.data;
            commit('setIpList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function getMacBlackList ({commit}, {page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_MAC_BLACKLIST(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result, status} = response.data;
            commit('setMacList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function addIpBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_IP_BLACKLIST(payload).then(response => {
            const {result, status} = response.data;
            commit('addIpBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function addMacBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_MAC_BLACKLIST(payload).then(response => {
            const {result, status} = response.data;
            commit('addMacBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function deleteIpBlackList ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_IP_BLACKLIST(id).then(response => {
            const {result, status} = response.data;
            commit('deleteIpBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function deleteMacBlackList ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_MAC_BLACKLIST(id).then(response => {
            const {result, status} = response.data;
            commit('deleteMacBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })   
}

export function getClientsBlackList ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_BLACKLIST_CLIENTS(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result, status} = response.data;
            commit('setClientsBlackList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getClientsWhiteList ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_WHITELIST_CLIENTS(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result, status} = response.data;
            commit('setClientsWhiteList', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function deleteClientBlackList ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_CLIENT_BLACKLIST(id).then(response => {
            const {result, status} = response.data;
            commit('deleteClientBlackList', id)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function deleteClientWhiteList ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_CLIENT_WHITELIST(id).then(response => {
            const {result, status} = response.data;
            commit('deleteClientWhiteList', id)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function addClientBlackList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_CLIENT_TO_BLACKLIST(payload).then(response => {
            const {result, status} = response.data;
            commit('addClientBlackList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function addClientWhiteList ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_CLIENT_TO_WHITELIST(payload).then(response => {
            const {result, status} = response.data;
            commit('addClientWhiteList', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getAllScenarios ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_SCENARIOS(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result, status} = response.data;
            commit('setScenarios', result.items)
            resolve(result.count)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })
}
"application/json; charset=utf-8"

export function editScenario ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_SCENARIO(payload).then(response => {
            const {result, status} = response.data;
            commit('changeScenario', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getAllUsers ({commit}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_USERS().then(response => {
            const {result, status} = response.data;
            commit('setUsers', result.items)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function getAllRoles ({commit}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_ROLES().then(response => {
            const {result, status} = response.data;
            commit('setRoles', result.items)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function signup ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AuthService.SIGN_IN(payload).then(response => {
            const {result} = response.data
            commit('addUser', result)
            resolve(result)
        })
        .catch(err => {
            // commit('some error')
            console.log(err.message)
            reject(err)
        })
    }) 
}

export function changeUserRole ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.CHANGE_ROLE_BY_USERID(payload.user_id, payload.role_id).then(response => {
            const {status} = response.data;
            commit('changeRole', payload)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function addNewRole ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_NEW_ROLE(payload).then(response => {
            const {result, status} = response.data;
            commit('addRole', result)
            resolve(status)
        })
    })
}   

export function deleteUser ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_USER_BY_ID(id).then(response => {
            const {status} = response.data;
            commit('deleteUser', id)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    }) 
}

export function deleteRole ({commit}, id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_ROLE_BY_ID(id).then(response => {
            const {status} = response.data;
            commit('deleteRole', id)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })  
}

export function editRole ({commit}, role) {
    return new Promise((resolve, reject) => {
        AdminService.EDIT_ROLE(role).then(response => {
            const {result, status} = response.data
            commit('updateRole', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })
}

export function editUser ({commit}, role) {
    return new Promise((resolve, reject) => {
        AdminService.EDIT_USER(role).then(response => {
            const {result, status} = response.data
            commit('updateUser', result)
            resolve(status)
        })
        .catch(err => {
            // commit('some error')
            reject(err)
        })
    })
}