import { editParamsRule1 } from "./mutations"

export function getUserById (state) {
    return function (id) {
        return state.users.find(user => user.id == id)
    }
}

export function getRoleById (state) {
    return function (id) {
        return state.roles.find(role => role.id == id)
    }
}


export function getTransferLimitScore (state) {
    return state.transferLimitParams.filter(param => param.key == 'DefaultScore')
}

export function getTrasnferLimitClientType (state) {
    return state.transferLimitParams.filter(param => param.key !== 'DefaultScore')
}

