

// emailNotifications

export function setEmployeesBlackList (state, blacklist) {
    state.employeeBlackList = blacklist;
}

export function setWay4CardOperationFrequency (state, way4cardoperationfrequency) {
    state.way4cardoperationfrequency = way4cardoperationfrequency
}

export function setWay4AllTransactions (state, way4transactions) {
    state.way4alltransactions = way4transactions
}

export function setWay4CardFrequency (state, way4cardfrequency) {
    state.way4cardfrequency = way4cardfrequency
}

export function setRules10Param (state, params) {
    state.rules10 = params
}

export function editRules10Param (state, params) {
    state.rules10 = state.rules10.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    }) 
}

export function setEmailNotifications (state, notifications) {
   state.emailNotifications = notifications 
}

export function addEmailNotification (state, notification) {
    state.emailNotifications = [...state.emailNotifications, notification]
}

export function editEmailNotification (state, notification) {
    state.emailNotifications = state.emailNotifications.map(n => {
        if (n.id == notification.id) {
            return notification
        } else {
            return n
        }
    })
}

export function deleteEmailNotification (state, id) {
    const index = state.emailNotifications.findIndex(notification => notification.id == id)
    state.emailNotifications.splice(index,1) 
}

export function setReport1 (state , report) {
    state.report1 = report;
}

export function setReport2 (state , report) {
    state.report2 = report;
}

export function setReport3 (state , report) {
    state.report3 = report;
}

export function setReport4 (state , report) {
    state.report4 = report;
}

export function setReport5 (state , report) {
    state.report5 = report;
}

export function setReport6 (state , report) {
    state.report6 = report;
}

export function setReport7 (state , report) {
    state.report7 = report;
}

export function setReport8 (state , report) {
    state.report8 = report;
}

export function setReport9 (state , report) {
    state.report9 = report;
}

export function setReport10 (state, report) {
    state.report10 = report
}

export function setNotTypicalAmountServicePercent(state, percents) {
    state.notTypicalAmountPercent = percents
} 

export function setDjList (state, djlist) {
    state.djList = djlist
}

export function setEventTypeList(state, eventList) {
    state.eventTypeList = eventList
}

export function setIntegrationList(state, integrationlist) {
    state.integrationList = integrationlist
}

export function editIntegrationList(state, integrationItem) {
    state.integrationList = state.integrationList.map(l => {
        if (l.id == integrationItem.system_id) {
            return {
                ...l, 
                open_status: integrationItem.open_status
            }
        } else {
            return l
        }
    }) 
}


export function setSystemList(state, systemlist) {
    state.systemList = systemlist
}

export function setScoringList (state, scoring) {
    state.scoringList = scoring
}

export function setBlackListCards (state, cards) {
    state.blackListCards = cards;
}

export function addBlackListCard (state, card) {
    state.blackListCards = [...state.blackListCards, card]
}

export function editBlackListCard (state, card) {
    state.blackListCards = state.blackListCards.map(c => {
        if (c.id == card.id) {
            return card
        } else {
            return c
        }
    })
}

export function deleteBlackListCard (state, id) {
    const index = state.blackListCards.findIndex(card => card.id == id)
    state.blackListCards.splice(index,1) 
}


export function addScoringListItem (state, scoring) {
    state.scoringList = [...state.scoringList, scoring]
}

export function editScoringListItem (state, scoring) {
    state.scoringList = state.scoringList.map(s => {
        if (s.id == scoring.id) {
            return scoring
        } else {
            return s
        }
    })
}

export function deleteScoringListItem (state, id) {
    const index = state.scoringList.findIndex(score => score.id == id)
    state.scoringList.splice(index,1) 
}

export function setTransferLimitParams (state, params) {
    state.transferLimitParams = params
}

export function editTransferLimitParams(state, params) {
    state.transferLimitParams = state.transferLimitParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    }) 
}

export function editNotTypicalAmountServicePercent(state,percent) {
    state.notTypicalAmountPercent = state.notTypicalAmountPercent.map(p => {
        if (p.id == percent.id) {
            return percent
        } else {
            return p
        }
    })
}

export function deleteNotTypicalAmountServicePercent(state,id) {
    const index = state.notTypicalAmountPercent.findIndex(percent => percent.id == id)
    state.notTypicalAmountPercent.splice(index,1) 
}

export function addNotTypicalAmountServicePercent (state,percent) {
    state.notTypicalAmountPercent = [...state.notTypicalAmountPercent, percent] 
}

export function setNotTypicalAmountParams (state, params) {
    state.notTypicalAmountParams = params
}

export function editNotTypicalAmountServiceParams (state , params) {
    state.notTypicalAmountParams = state.notTypicalAmountParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setFinancialSpeedPaymentParams (state, params) {
    state.financialSpeedPaymentParams = params
}

export function setNewPayeeParams (state, params) {
    state.newPayeeParams = params
}

export function editNewPayeeParams (state, params) {
    state.newPayeeParams = state.newPayeeParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setPaymentProfileParams (state, params) {
    state.paymentProfileParams = params
}

export function editPaymentProfileParams (state, params) {
    state.paymentProfileParams = state.paymentProfileParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setInactiveAccParams(state, params) {
    state.inActiveAccParams = params
}

export function editInactiveAccParams (state, params) {
    state.inActiveAccParams = state.inActiveAccParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setPaymentTrendParams(state, params) {
    state.paymentTrendParams = params
}

export function editPaymentTrendParams (state, params) {
    state.paymentTrendParams = state.paymentTrendParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setAgeCheckerParams(state, params) {
    state.ageCheckerParams = params
}

export function editAgeCheckerParams (state, params) {
    state.ageCheckerParams = state.ageCheckerParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}

export function setNewCardTransactionParams(state, params) {
    state.newCardTransactionParams = params
}

export function editNewCardTransactionParams (state, params) {
    state.newCardTransactionParams = state.newCardTransactionParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    })
}


export function setHdbkProducts (state,products) {
    state.hdbkProducts = products
}

export function updateCaseButtons (state, status) {
    state.hdbksolutionlist = state.v.map(s => {
        if (s.key == status.key) {
            return status
        } else {
            return s
        }
    })
}

export function setSolutionListCaseButtons(state, statuses) {
    state.hdbksolutionlist = statuses
}



export function setStatuses (state, statuses) {
    state.statuses = statuses
}

export function setStatusesSystem (state, statuses) {
    state.statusessystem = statuses
}

export function setFinancialLimitParams (state,params) {
    state.financialLimitParams = params
}

export function setMultipleLoginParams (state, params) {
    state.multipleLoginParams = params
}

export function setIpExternal (state, list) {
    state.ipexternal = list
}

export function setTerroristSsiIndv (state, list) {
    state.terroristSsiIndv = list
}

export function setTerroristSsiLe (state,list) {
    state.terroristSsiLe = list
}

export function setTerroristOfacIndv (state,list) {
    state.terroristOfacIndv = list
}

export function setTerroristOfacLe (state, list) {
    state.terroristOfacLe = list
}

export function setTerroristKfmIndv (state, list) {
    state.terroristKfmIndv = list
}

export function setTerroristKfmLe (state, list) {
    state.terroristKfmLe = list
}

export function setTerroristEsIndv (state, list) {
    state.terroristEsIndv = list
}

export function setTerroristsOonIndv(state, list) {
    state.terroristsOonIndv = list
}

export function setRules1Param(state, params) {
    state.rules1params = params
}

export function setTerroristsOonLe(state, list) {
    state.terroristsOonLe = list
}

export function setTalibanTerroristsIndv(state,list) {
    state.talibanTerroristsIndv = list
}

export function setTalibanTerroristsLe(state,list) {
    state.talibanTerroristsLe = list
}

export function addFinancialSpeedPaymentParam(state, params) {
    state.financialSpeedPaymentParams = [...state.financialSpeedPaymentParams, params]
}

export function editFinancialSpeedPaymentParams(state, params) {
    state.financialSpeedPaymentParams = state.financialSpeedPaymentParams.map(p => {
        if (p.id == params.id) {
            return params
        } else {
            return p
        }
    }) 
}

export function editPhoneBlackList(state,phone) {
    state.phonelist = state.phonelist.map(p => {
        if (p.id == phone.id) {
            return phone
        } else {
            return p
        }
    })
}

export function editIpBlackList(state, ip) {
    state.iplist = state.iplist.map(i => {
        if (ip.id == i.id) {
            return ip
        } else {
            return i
        }
    }) 
}

export function editMacBlackList(state, mac) {
    state.maclist = state.maclist.map(m => {
        if (mac.id == m.id) {
            return mac
        } else {
            return m
        }
    }) 
}

export function editClientBlackList(state, client) {
    state.clientsblacklist = state.clientsblacklist.map(c => {
        if (c.id == client.id) {
            return client
        } else {
            return c
        }
    })
}

export function editFinancialLimitParams(state, params) {
    state.financialLimitParams = state.financialLimitParams.map(p => {
        if (p.id == params.id) {
            return params
        } else {
            return p
        }
    })
}

export function editLoginParams(state, params) {
    state.multipleLoginParams = state.multipleLoginParams.map(p => {
        if (p.key == params.key) {
            return params
        } else {
            return p
        }
    }) 
}

export function editParamsRule1(state, params) {
    state.rules1params = state.rules1params.map(p => {
        if (p.id == params.id) {
            return params
        } else {
            return p
        }
    }) 
}

export function editClientWhiteList(state,client) {
    state.clientswhitelist = state.clientswhitelist.map(c => {
        if (c.id == client.id) {
            return client
        } else {
            return c
        }
    }) 
}

export function addFinancialLimitParams(state, param) {
    state.financialLimitParams = [...state.financialLimitParams, param]
}

export function setPhoneList(state, phonelist) {
    state.phonelist = phonelist
}

export function addPhoneBlackList(state, phone) {
    state.phonelist = [...state.phonelist,phone] 
}

export function deletePhoneBlackList(state,phone) {
    const index = state.phonelist.findIndex(item => item.phone == phone)
    state.phonelist.splice(index,1)  
}

export function deleteFinancialLimitParams(state,id) {
    const index = state.financialLimitParams.findIndex(item => item.id == id)
    state.financialLimitParams.splice(index,1)
}

export function deleteFinancialSpeedPaymentParams(state,id) {
    const index = state.financialSpeedPaymentParams.findIndex(item => item.id == id)
    state.financialSpeedPaymentParams.splice(index,1) 
}

export function setIpList(state, iplist) {
    state.iplist = iplist
}

export function setMacList(state, maclist) {
    state.maclist = maclist
}

export function addIpBlackList(state, ip) {
    state.iplist = [...state.iplist,ip]
}

export function addMacBlackList(state, mac) {
    state.maclist = [...state.maclist,mac] 
} 

export function deleteIpBlackList(state, ip) {
    const index = state.iplist.findIndex(item => item.ip == ip)
    state.iplist.splice(index,1) 
}

export function deleteMacBlackList(state, mac) {
    const index = state.maclist.findIndex(item => item.mac == mac)
    state.maclist.splice(index,1) 
}

export function setClientsBlackList(state,clientsblacklist) {
    state.clientsblacklist = clientsblacklist
}

export function setClientsWhiteList(state, clientswhitelist) {
    state.clientswhitelist = clientswhitelist
}

export function setScenarios (state,scenarios) {
    state.scenarios = scenarios
}

export function addClientBlackList (state, client) {
    state.clientsblacklist = [...state.clientsblacklist,client]
}

export function addClientWhiteList (state,client) {
    state.clientswhitelist = [...state.clientswhitelist,client] 
}

export function deleteClientBlackList(state, id) {
    const index = state.clientsblacklist.findIndex(client => client.id == id)
    state.clientsblacklist.splice(index,1)
}

export function deleteClientWhiteList(state, id) {
    const index = state.clientswhitelist.findIndex(client => client.id == id)
    state.clientswhitelist.splice(index,1)
}

export function changeScenario (state, payload) {
    const {id} = payload
    state.scenarios = state.scenarios.map(scenario => {
        if (scenario.id == id) {
            return payload
        } else {
            return scenario
        }
    })
}

export function deleteScenario(state, id) {
    const index = state.scenarios.findIndex(scenario => scenario.id == id)
    state.scenarios.splice(index,1)
}

export function setUsers (state, users) {
    state.users = users;
}

export function changeRole (state, payload) {
    const {user_id, role_id} = payload;
    state.users = state.users.map(user => {
        if (user.id == user_id) {
            return {
                ...user,
                role_id
            }
        } else {
            return user
        }
    })
}

export function addUser (state, user) {
    state.users = [...state.users,user]
}

export function setRoles(state, roles) {
    state.roles = roles
}

export function addRole(state,role) {
    state.roles = [...state.roles, role]
}

export function deleteUser(state, id) {
    const index = state.users.findIndex(user => user.id == id)
    state.users.splice(index,1)
}

export function deleteRole(state,id) {
    const index = state.roles.findIndex(role => role.id == id)
    state.roles.splice(index,1)
}

export function setHistoryStorageSettings(state, history) {
    state.historyStorageSettingsList = history
}

export function updateHistoryStorageSettings(state, history) {
    state.historyStorageSettingsList = state.historyStorageSettingsList.map(h => {
        if (history.key == h.key) {
            return history
        } else {
            return h
        }
    })
}


export function updateRole(state, role) {
    state.roles = state.roles.map(r => {
        if (role.id == r.id) {
            return role
        } else {
            return r
        }
    })
}

export function updateUser(state, user) {
    state.users = state.users.map(u => {
        if (u.id == user.id) {
            return user
        } else {
            return u
        }
    })
}
