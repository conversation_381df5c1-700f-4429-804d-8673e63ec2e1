
import AdminService from 'src/services/admin'
import {setExportExcel} from "src/store/cases/mutations";

export function changeCaseAssignee ({commit}, form) {
    return new Promise((resolve,reject) => {
        AdminService.CHANGE_CASE_ASSIGNEE(form).then(response => {
            const { result } = response.data;
            commit('changeCaseAssignee', result)
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function deleteComment ({commit} , id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_COMMENT(id).then(response => {
            const { result } = response.data;
            commit('removeComment', result)
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getCaseComments ({commit}, case_id) {
    return new Promise((resolve,reject) => {
        AdminService.GET_CASE_COMMENTS(case_id).then(response => {
            const { result } = response.data;
            commit('setCaseComments', result)
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function editCaseComment ({commit}, comment) {
    return new Promise((resolve,reject) => {
        AdminService.EDIT_CASE_COMMENT(comment).then(response => {
            const { result } = response.data;
            commit('editCaseComment', result)
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function addComment ({commit}, comment) {
    return new Promise((resolve,reject) => {
        AdminService.ADD_CASE_COMMENT(comment).then(response => {
            const { result } = response.data;
            commit('addCaseComment', result)
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function deleteFileById ({commit}, file_id) {
    return new Promise((resolve,reject) => {
        AdminService.DELETE_FILE_INFO_BY_FILE_ID(file_id).then(response => {
            const { result } = response.data;
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getFileInfoByFileId ({commit},file_id) {
    return new Promise((resolve,reject) => {
        AdminService.GET_FILE_INFO_BY_FILE_ID(file_id).then(response => {
            const { result } = response.data;
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getAllCases ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_CASES(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result} = response.data
            commit('setCases', result.items || [])
            resolve(result.count)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}


export function getAllCasesDetailed ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values="", start_date = "", end_date = ""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_CASES_DETAILED(page,per_page, sort_field,sort_order,filter_fields, filter_values, start_date, end_date).then(response => {
            const {result} = response.data
            commit('setCasesDetailed', result.items || [])
            resolve(result.count)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getExportExcel({ commit }, { start_date = "", end_date = "" }) {
  return new Promise((resolve, reject) => {
    AdminService.GET_EXPORT_EXCEL(start_date, end_date)
      .then(response => {
        if (response.status === 200) {
          commit("setExportExcel", response.data);
          resolve(response);
        } else {
          reject(new Error("Ошибка при скачивании файла"));
        }
      })
      .catch(err => {
        console.log(err.message);
        reject(err);
      });
  });
}


export function getAllRelatedTransfers({ commit }, { page = 1, per_page = 100, sort_field = "oper_date_time", sort_order = "asc", filter_fields = "", filter_values = "", start_date = "", end_date = "",iin="" }) {
    return new Promise((resolve, reject) => {
        AdminService.GET_ALL_RELATED_TRANSFERS(page, per_page, sort_field, sort_order, filter_fields, filter_values, start_date, end_date,iin).then(response => {
            const { result } = response.data
            commit('setRelatedTransfers', result.items || [])
            resolve(result.count)
        })
            .catch(err => {
                console.log(err.message)
                reject(err)
            })
    })
}




export function getAllCasesWork ({commit},{page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_ALL_CASES(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result} = response.data
            commit('setCasesWork', result.items || [])
            resolve(result.count)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}



export function updateCase ({commit}, payload) {
    return new Promise((resolve,reject) => {
        AdminService.UPDATE_CASE(payload).then(response => {
            const {result} = response.data
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getCaseById({commit}, {page=1, per_page=100,sort_field="id", sort_order="asc",filter_fields="id",filter_values=""}) {
    return new Promise((resolve,reject) => {
        AdminService.GET_CASE_BY_ID(page,per_page, sort_field,sort_order,filter_fields, filter_values).then(response => {
            const {result} = response.data
            commit('setCase', result.items[0])
            resolve(result)
        })
        .catch(err => {
            console.log(err.message)
            reject(err)
        })
    })
}

export function getCaseEvents ({commit}, case_id) {
    return new Promise((resolve,reject) => {
        AdminService.GET_CASE_EVENTS(case_id).then(response => {
            const {result} = response.data
            if (result) {
            commit('setCaseEvents', result.event)
            commit('setCaseEventStatus', result.status)
            resolve(result ? result : {})
            }
        })
        .catch(err => {
            console.log(err)
            reject(err)
        })
    })
}
