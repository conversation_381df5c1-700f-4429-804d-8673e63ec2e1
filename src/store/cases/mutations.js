export function setCases (state, cases) {
    state.cases = cases;
}


export function setCasesWork(state, cases) {
    state.casesWork = cases
}

export function setCaseComments (state, comments) {
    state.caseComments = comments
}

export function removeComment (state, comment) {
    state.caseComments = state.caseComments.filter(c => c.id !== comment.id)
}

export function editCaseComment (state, comment) {
    const index = state.caseComments.findIndex(x => x.id == comment.id)
    state.caseComments.splice(index,1,comment)
}

export function addCaseComment (state, comment) {
    state.caseComments = [...state.caseComments, comment]
}

export function setCaseEvents (state, caseEvents) {
    state.caseEvents = caseEvents
}

export function setCase (state, val) {
    state.case = val
}
export function setCasesDetailed  (state, val) {
    state.casesDetailed  = val
}

export function setExportExcel  (state, val) {
  state.exportReport  = val
}

export function setRelatedTransfers(state, related_transfers) {
    state.related_transfers = related_transfers;
  }


export function changeCaseAssignee (state, val) {
    state.case = val
}


export function setCaseEventStatus (state,val) {
    state.caseEventStatus = val
}
