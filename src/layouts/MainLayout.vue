<template>
  <q-layout view="lHh LpR lFf">
    <q-header reveal :class="$q.dark.isActive ? 'header_dark' : 'header_normal'">
      <q-toolbar>
        <q-btn @click="left = !left" flat dense icon="menu" class="q-mr-sm  text-white " />

        <q-space />

        <q-select v-model="lang" :options="langOptions" dense id="langswitch" borderless emit-value map-options
          options-dense class="q-mr-md" />
        <q-btn class="q-mr-md" color="white" round flat icon="notifications" />

        <div class="column q-mr-md">
          <span style="font-size: 14px" class="col text-white">
            {{
            user && user.first_name && user.last_name
            ? user.first_name + " " + user.last_name
            : "ФИО"
            }}
          </span>
          <span style="font-size: 12px" class="col text-grey">
            {{ user && user.role ? currLang == 'ru' ? user.role.name_ru : user.role.name_kz : "Должность" }}
          </span>
        </div>
        <q-btn flat round dense color="white" icon="fas fa-sign-out-alt" @click="logoutNotify" to="/" />
      </q-toolbar>
    </q-header>

    <q-drawer class="left-navigation text-white" v-model="left"
      style="background-image: url(https://demos.creative-tim.com/vue-material-dashboard/img/sidebar-2.32103624.jpg) !important;"
      side="left" elevated>
      <div class="full-height drawer_bg">
        <div style="height: calc(100% - 117px);padding:10px;">
          <q-toolbar @click="$router.push({ name: 'cases' })" style="cursor:pointer;">
            <svg width="243" height="28" viewBox="0 0 243 28" fill="none" xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink">
              <rect width="243" height="28" fill="url(#pattern0)" />
              <defs>
                <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                  <use xlink:href="#image0" transform="translate(0 -0.00511998) scale(0.00078125 0.00678013)" />
                </pattern>
                <image id="image0" width="1280" height="149"
                  xlink:href="data:image/png;base64,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" />
              </defs>
            </svg>
          </q-toolbar>

          <q-scroll-area style="height:100%;">
            <q-tab-panels v-model="panel" animated class="bg-transparent">
              <q-tab-panel name="home" class="no-padding">
                <q-list>
                  <template v-for="(menuItem, index) in menuList">
                    <q-item :key="index" clickable @click="mainMenuClick(menuItem)" active-class="tab-active"
                      :active="link === menuItem.name" :disable="menuItem.is_disabled" :to="`/${menuItem.to}`"
                      class="q-ma-sm navigation-item " v-ripple>
                      <q-item-section avatar>
                        <q-icon :name="menuItem.icon"></q-icon>
                      </q-item-section>

                      <q-item-section>
                        {{ menuItem.label }}
                      </q-item-section>
                    </q-item>

                    <q-separator :key="'sep' + index" v-if="menuItem.separator"></q-separator>
                  </template>
                </q-list>
              </q-tab-panel>

              <q-tab-panel name="settings" class="no-padding">
                <q-list>
                  <q-item class="q-ma-sm navigation-item" active-class="tab-active" clickable @click="panel = 'home'"
                    v-ripple>
                    <q-item-section avatar>
                      <q-icon name="chevron_left"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      {{ $t("mainMenu") }}
                    </q-item-section>
                  </q-item>
                  <q-separator></q-separator>

                  <q-item to="/integration" class="q-ma-sm navigation-item " active-class="tab-active" clickable
                    v-ripple>
                    <q-item-section>
                      {{ $t("integrationChannelSettings") }}
                    </q-item-section>
                  </q-item>

                  <q-item to="/scoring" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section>
                      {{ $t("statusSettings") }}
                    </q-item-section>
                  </q-item>

                  <q-item to="/casestatusessettings" class="q-ma-sm navigation-item " active-class="tab-active"
                    clickable v-ripple>
                    <q-item-section>
                      {{ $t("caseStatusesSettings") }}
                    </q-item-section>
                  </q-item>


                  <q-item-section>
                    {{ $t("versionSettings") }}
                  </q-item-section>
                  </q-item>

                  <q-item to="/emailnotifications" class="q-ma-sm navigation-item " active-class="tab-active" clickable
                    v-ripple>
                    <q-item-section>
                      {{ $t("notificationSettings") }}
                    </q-item-section>
                  </q-item>

                  <q-item to="/datastorage" class="q-ma-sm navigation-item " active-class="tab-active" clickable
                    v-ripple>
                    <q-item-section>
                      {{ $t("storageSettings") }}
                    </q-item-section>
                  </q-item>

                  <q-item to="/dowjones" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section>
                      {{ $t("djListSettings") }}
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-tab-panel>

              <q-tab-panel name="configsettings" class="no-padding">
                <q-list>
                  <q-item class="q-ma-sm navigation-item" active-class="tab-active" clickable @click="panel = 'home'"
                    v-ripple>
                    <q-item-section avatar>
                      <q-icon name="chevron_left"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      {{ $t("mainMenu") }}
                    </q-item-section>
                  </q-item>
                  <q-separator></q-separator>
                  <!-- <q-item clickable v-ripple @click="panel = 'more'">
                    <q-item-section avatar>
                      <q-icon name="inbox"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      More
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon name="chevron_right"></q-icon>
                    </q-item-section>
                  </q-item> -->

                  <q-item to="/scenarios_management" class="q-ma-sm navigation-item " active-class="tab-active"
                    clickable v-ripple>
                    <q-item-section>
                      {{ $t("allRules") }}
                    </q-item-section>
                  </q-item>

                  <q-item to="/rules1" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #1 </q-item-section>
                  </q-item>

                  <q-item to="/rules2" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #2 </q-item-section>
                  </q-item>

                  <q-item to="/rules3" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #3 </q-item-section>
                  </q-item>

                  <q-item to="/rules4" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #4 </q-item-section>
                  </q-item>

                  <q-item to="/rules5" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #5 </q-item-section>
                  </q-item>

                  <q-item to="/rules6" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #6 </q-item-section>
                  </q-item>

                  <q-item to="/rules7" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #7 </q-item-section>
                  </q-item>

                  <q-item to="/rules8" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #8 </q-item-section>
                  </q-item>

                  <q-item to="/rules9" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #9 </q-item-section>
                  </q-item>

                  <q-item to="/rules10" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #10 </q-item-section>
                  </q-item>
                  <q-item to="/rules11" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #11 </q-item-section>
                  </q-item>

                  <q-item to="/rules12" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #12 </q-item-section>
                  </q-item>
                  <q-item to="/rules13" class="q-ma-sm navigation-item " active-class="tab-active" clickable v-ripple>
                    <q-item-section> {{ $t("rule") }} #13 </q-item-section>
                  </q-item>

                </q-list>
              </q-tab-panel>

              <!-- <q-tab-panel name="more" class="no-padding">
                <q-list>
                  <q-item clickable @click="panel = 'settings'" v-ripple>
                    <q-item-section avatar>
                      <q-icon name="chevron_left"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      More
                    </q-item-section>
                  </q-item>
                  <q-separator></q-separator>
                  <q-item clickable v-ripple>
                    <q-item-section avatar>
                      <q-icon name="inbox"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      Some Item
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="outbox"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      Child Item 2
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-tab-panel> -->
            </q-tab-panels>
          </q-scroll-area>
        </div>
      </div>
    </q-drawer>

    <q-page-container>
      <q-page class="row no-wrap">
        <div class="col">
          <div class="full-height">
            <q-scroll-area class="col full-height" visible>
              <router-view />
            </q-scroll-area>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  data() {
    return {
      lang: this.$i18n.locale,
      langOptions: [
        { value: "ru", label: "RU" },
        { value: "kz", label: "KZ" }
      ],
      left: true,
      panel: "home",
      tab: "mails",
      tab2: "mails",
      link: "cases"
    };
  },
  mounted () {
    const { lang='ru' } = this.$route.query;
    this.lang = this.langOptions.find(x => x.value == lang).value;
  },
  watch: {
    lang(lang, prevlang) {
      this.changeLang(lang)
    }
  },
  computed: mapState({
    user: state => state.auth.currentUser,
    menuList() {
      return [
        {
          icon: "people",
          label: this.$t("users"),
          name: "customer_management",
          separator: false,
          to: "customer_management",
          is_disabled: false
        },
        {
          icon: "list",
          label: this.$t("rolesDict"),
          name: "roles_management",
          to: "roles_management",
          separator: true,
          is_disabled: false
        },
        {
          icon: "devices_other",
          label: this.$t("cases"),
          name: "cases",
          to: "cases",
          separator: false,
          is_disabled: false
        },
        {
          icon: "payments",
          label: this.$t("smp"),
          name: "smp",
          to: "smp",
          separator: false,
          is_disabled: false
        },
        {
          icon: "swap_horiz",
          label: this.$t("money_transfers"),
          name: "money_transfers",
          to: "money_transfers",
          separator: false,
          is_disabled: false
        },
        {
          icon: "payments",
          label: this.$t("pensioners_transfers"),
          name: "pensioners_transfers",
          to: "pensioners_transfers",
          separator: false,
          is_disabled: false
        },
        {
          icon: "work",
          label: this.$t("casesWork"),
          name: "casesatwork",
          to: "reportsall4onepage",
          separator: false,
          is_disabled: false
        },
        {
          icon: "bar_chart",
          to: "reports",
          label: this.$t("analytics"),
          is_disabled: false,
          name: "dashboard",
          separator: true
        },
        {
          icon: "settings",
          label: this.$t("settings"),
          to: "scoring",
          separator: false,
          is_disabled: false,
          name: "settings",
          child: [
            {
              icon: "settings",
              label: "Settings",
              separator: true
            }
          ]
        },
        {
          icon: "rule",
          label: this.$t("ruleConfigurations"),
          to: "scenarios_management",
          separator: false,
          is_disabled: false,
          name: "configsettings",
          child: [
            {
              icon: "settings",
              label: "Settings",
              separator: true
            }
          ]
        },
        {
          icon: "account_circle",
          to: "my_profile",
          label: this.$t("profile"),
          name: "profile",
          separator: false
        }
      ];
    }
  }),
  methods: {
    ...mapActions({
      logout: "auth/logout"
    }),

    changeLang(lang) {
      this.$i18n.locale = lang;
      if (lang == "ru") {
        import(`quasar/lang/${lang}`).then(language => {
          localStorage.setItem(`language`, language.default.isoName);
          this.$q.lang.set(language.default);
        });
      } else {
        import(`../i18n/kz/framework`).then(language => {
          localStorage.setItem(`language`, language.default.isoName);
          this.$q.lang.set(language.default);
        });
      } 
    },
    mainMenuClick(mItem) {
      this.link = mItem.name;
      if (mItem.child) {
        this.panel = mItem.name;
      }
    },
    logoutNotify() {
      this.logout()
        .then(response => {
          if (response) {
            this.$q.notify({
              message: "Вы успешно вышли из системы"
            });
          }
        })
        .catch(err => {
          this.$q.notify({
            message: "Произлошла Ошибка"
          });
        });
    }
  }
};
</script>

<style>
.q-drawer {
  background-image: url("https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixid=MnwxMjA3fDB8MHxzZWFyY2h8Nnx8bW91bnRhaW5zfGVufDB8fDB8fA%3D%3D&ixlib=rb-1.2.1&w=1000&q=80") !important;
  background-size: cover !important;
}

.navigation-item {
  border-radius: 10px;
  font-weight: normal;
  font-size: 15px;
  line-height: 26px;
  /* identical to box height, or 162% */
  letter-spacing: -0.355556px;
  /* Basic / Black */
  color: #032341;
}

.tab-active {
  background-color: #e8eaf9;
  border-radius: 10px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 26px;
  /* identical to box height, or 162% */
  letter-spacing: -0.355556px;
  /* Basic / Black */
  color: #12022f;
  mix-blend-mode: normal;
}

body {
  background: #f0f4fd !important;
}

.drawer_bg {
  background-color: rgb(255, 255, 255);
}

.header_normal {
  background: #0043c4;
}

.header_dark {
  background: linear-gradient(145deg, rgb(61, 14, 42) 15%, rgb(14, 43, 78) 70%);
}

#langswitch {
  color: white !important;
}

.q-select__dropdown-icon {
  color: white !important;
}
</style>
