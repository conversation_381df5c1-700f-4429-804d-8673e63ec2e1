<template>
  <CustomTable
    :data="hdbksolutionlist"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editCaseStatusesInner"
    :tabletitle="$t('caseStatusesSettings')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="!caseStatuses.value"
    :isAddAvailable="false"
    :loading="loading"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:key="{props}">
       <q-item>
        <q-item-section>
          <q-item-label>
            {{  props.row.key == 'CreateCase' ? $t('shouldCreateCase') : $t('stopEvent')}}
          </q-item-label>
        </q-item-section>
      </q-item> 
    </template>


     <template v-slot:value="{props}">
       <q-item>
        <q-item-section>
          <q-item-label>
            {{  props.row.value == 'true' ? $t('yes') : $t('no')}}
          </q-item-label>
        </q-item-section>
      </q-item> 
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                     <q-item-label class="q-pb-xs">
                  {{$t('value')}}
                </q-item-label>
                  <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('value')"
                  dense
                  v-model="caseStatuses.value"
                  :options="[{key: 1, value: `true`}, {key: 2, value: `false`}]"
                  :option-label="opt => opt.key == 1 ? `${$t('yes')}` : `${$t('no')}`"
                  option-value="value"
                  emit-value
                  map-options
                />  
              </q-item-section>
            </q-item>


          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
        caseStatuses: {
            name: "",
            value: "false",
            key: "",
            disabled: false,
        },
 
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      hdbksolutionlist: state => state.admin.hdbksolutionlist,
    }),

      columns() { 
        return [
        {
          name: "key",
          label: this.$t('columnDescription'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]},
    
  },

  methods: {
    ...mapActions({
     getSolutionListCaseButtons: "admin/getSolutionListCaseButtons",
      updateCaseButtons: "admin/updateCaseButtons"
    }),

      convertCaseStatusSystemVal(val) {
      if (val == 'Запрещено') {
        return this.$t('forbidden')
      } else if (val == 'Разрешено') {
        return this.$t('allowed')
      } else if(val == 'Приостановлено') {
        return this.$t('suspended')
      } else if(val == 'На анализе') {
        return this.$t('onanalys')
      } else {
        return this.$t('unabletocheck')
      }
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getSolutionListCaseButtons({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("name_ru"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.caseStatuses = {
        ...row
      };
    },

    reset() {
      this.caseStatuses = {
        value: "",
        disabled: false
      };
    },

    editCaseStatusesInner() {
      this.updateCaseButtons(this.caseStatuses)
        .then(response => {
          this.$q.notify({
            message: `Статус успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
  }
};
</script>
