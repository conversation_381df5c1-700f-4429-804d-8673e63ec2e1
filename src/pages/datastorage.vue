<template>
  <CustomTable
    :data="historyStorageSettingsList"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editHistoryStorage"
    :tabletitle="$t('storageSettings')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="
      !historyStorage.value || !timemeasure
    "
    :isAddAvailable="false"
    :loading="loading"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

      <template v-slot:key="{props}">
        {{convertKeyToRu(props.row.key)}}
      </template>

      <template v-slot:value="{props}">
        {{ convertTimeMeasurementToLang(props.row.value)}}
      </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="historyStorage.value"
                  type="number"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('measurement')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('measurement')"
                  dense
                  v-model="timemeasure"
                  :options="timesOptions"
                  option-label="value"
                  option-value="key"
                  emit-value
                  map-options
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      historyStorage: {
        key: "",
        name: "",
        value: ""
      },
      timemeasure: "",
      timesOptions: [
         {
          key: "year",
          value: "Год"
        },
        {
          key: "month",
          value: "Месяц"
        },
        {
          key: "hour",
          value: "Час"
        },
        {
          key: "minute",
          value: "Минута"
        },
        // {
        //   key: "second",
        //   value: "Секунда"
        // }
      ],
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      historyStorageSettingsList: state => state.admin.historyStorageSettingsList
    }),
    columns() {
      return [
        {
          name: "key",
          label: this.$t('columnName'),
          field: row => row.key,
          sortable: true,
          align: "center"
        },
        {
          name: "value",
          label: this.$t('value'),
          field: row => row.value,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getHistoryStorageList: "admin/getHistoryStorageList",
      updateHistoryStorageSettings: "admin/updateHistoryStorageSettings"
    }),

    convertKeyToRu(val) {
      if (val == 'financial') {
        return this.$t('finOperationFull')
      } else if (val =='profile') {
        return this.$t('profile')
      } else if (val == 'logs') {
        return this.$t('userActions')
      } else if (val == 'registration') {
        return this.$t('registrationandauthorization')
      } else {
        return ''
      }
    },

    convertToRu(val) {
      if (!val) {
        return ""
      }
      const text = val.split(" ")
      let secondpart = text[1].trim()
      let newtext = ""
      newtext += text[0] + " "
      if (secondpart == 'year') {
        newtext += 'Год'
      }  else if (secondpart == 'years') {
        newtext += 'Года'
      }  else if (secondpart == 'month') {
        newtext += 'Месяц'
      }  else if (secondpart == 'months') {
        newtext += 'Месяца'
      }  else if (secondpart == 'day') {
        newtext += 'День'
      }  else if (secondpart == 'days') {
        newtext += 'Дня'
      } 
      return newtext
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getHistoryStorageList({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("key"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
        this.historyStorage = { ...row, value: +row.value.match(/\d+/)[0] };
    },

    reset() {
      this.historyStorage = {
        key: "",
        name: "",
        value: ""
      };
      this.timemeasure = "";
    },

    generateForm(historyStorage) {
      if (+historyStorage.value !== 1) {
        return {
          ...historyStorage,
          value: historyStorage.value + " " + this.timemeasure + "s"
        };
      } else {
        return {
          ...historyStorage,
          value: historyStorage.value + " " + this.timemeasure
        };
      }
    },

    editHistoryStorage() {
      let form = {};

      if (+this.historyStorage.value < 0 || +this.historyStorage.value > 100) {
        this.$q.notify({
            message: `Значение быть в диапозоне 0-100`,
            color: "red"
        }); 
        return;
      }

    form = this.generateForm(this.historyStorage);

      this.updateHistoryStorageSettings(form)
        .then(response => {
          this.reset()
          this.$q.notify({
            message: `Параметр ${response.key} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

  }
};
</script>
