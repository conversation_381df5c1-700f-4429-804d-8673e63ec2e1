<template>
  <CustomTable
    :data="clientsblacklist"
    :columns="columns"
    @reset="reset"
    :tabletitle="$t('clientsBlackList')"
    :is_add_disabled="
      blacklistclient.iin.length < 12 || !/^\d+$/.test(blacklistclient.iin)
    "
    :is_edit_disabled="
      blacklistclient.iin.length < 12 || !/^\d+$/.test(blacklistclient.iin)
    "
    :isDeleteAvailable="true"
    :isAddAvailable="true"
    @addRow="addClientToBlackListInner"
    @onEditClick="onEditClick"
    @saveEdit="saveEdit"
    @deleteRow="deleteClientToBlackListInner"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    :loading="loading"
    @onRefresh="onRequest({ filter, pagination })"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeClient')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('iinbin')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  maxlength="12"
                  v-model="blacklistclient.iin"
                  outlined
                  :label="$t('iinbin')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  autogrow
                  dense
                  v-model="blacklistclient.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('addClient')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{$t('iinbin')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  maxlength="12"
                  v-model="blacklistclient.iin"
                  outlined
                  :label="$t('iinbin')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('notes')}}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="blacklistclient.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      blacklistclient: {
        iin: "",
        note: ""
      },
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      clientsblacklist: state => state.admin.clientsblacklist
    }),
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "iin",
          label: this.$t('iinbin'),
          field: row => row.iin,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getClientsBlackList: "admin/getClientsBlackList",
      addClientBlackList: "admin/addClientBlackList",
      deleteClientBlackList: "admin/deleteClientBlackList",
      editClientBlackList: "admin/editClientBlackList"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getClientsBlackList({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("id,iin"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.blacklistclient = { ...row };
    },

    saveEdit() {
      this.editClientBlackList(this.blacklistclient)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${response.id} изменен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    reset() {
      this.blacklistclient = {
        iin: "",
        note: ""
      };
    },

    deleteClientToBlackListInner(row) {
      this.deleteClientBlackList(row.id)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${row.id} удален из черного списка`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addClientToBlackListInner() {
      this.addClientBlackList(this.blacklistclient)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${this.blacklistclient.iin} добавлен в черный список`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
