<template>
  <UserTable :users="users" :roles="roles"
  @changeUserRoleInner="changeUserRoleInner" @addUser="addUser"
  @deleteUser="deleteUserInner"
  @editUserInner="editUserInner"/>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    UserTable: () => import("../components/UserTable")
  },

  created() {
    if (!this.users.length) {
      this.getAllUsers()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    if (!this.roles.length) {
      this.getAllRoles()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  },

  computed: mapState({
    users: state => state.admin.users,
    roles: state => state.admin.roles
  }),

  methods: {
    ...mapActions({
      getAllUsers: "admin/getAllUsers",
      changeUserRole: "admin/changeUserRole",
      getAllRoles: "admin/getAllRoles",
      signup: "admin/signup",
      deleteUser: "admin/deleteUser",
      editUser: "admin/editUser"
    }),

    editUserInner(user) {
      this.editUser(user)
        .then(response => {
          this.$q.notify({
            message: `Данные пользователя успешно обновленны `,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "red"
          });
        }); 
    },

    deleteUserInner(user) {
      this.deleteUser(user.id)
        .then(response => {
          this.$q.notify({
            message: `Пользователь ${user.first_name} ${user.last_name} успешно удален`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "red"
          });
        });
    },

    addUser(user) {
      this.signup(user)
        .then(response => {
          this.$q.notify({
            message: `Пользователь успешно создан`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "red"
          });
        });
    },

    changeUserRoleInner(payload) {
      this.changeUserRole(payload)
        .then(response => {
          this.$q.notify({
            message: `Роль успешно обновленна`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
