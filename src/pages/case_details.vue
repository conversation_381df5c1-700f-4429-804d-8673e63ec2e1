<template>
  <div class="q-px-xl q-pt-sm q-pb-lg" style="overflow-y: scroll; width: 100%;">
    <q-spinner
      v-if="loading"
      style="position: sticky; left: 45%; top: 45%;"
      color="primary"
      size="5em"
      :thickness="10"
    />

    <div :style="{ opacity: loading ? 0.2 : 1 }">
      <div class="column q-mt-md" v-if="returnbuttonneeded">
        <span class="arrow_back_text" @click="$router.push({ name: 'cases' })">
          <q-icon class="arrow_back_style" name="arrow_back" />
          <span class="q-ml-xs"> {{ $t("goBack") }} </span>
        </span>
      </div>
      <div class="q-pa-sm">
        <div class="q-mt-lg">
          <span class="case_num_text"
            >{{ $t("workedRules") }} - Кейс №{{ caseid }}</span
          >
        </div>

        <q-table
          title-class="titletable"
          :rows-per-page-options="[10, 20, 50, 0]"
          :data="data"
          class="q-mt-lg text3"
          :columns="columns"
          row-key="id"
          dense
        >
          <template v-slot:body-cell-status="props">
            <q-td>
              <span> В Работе </span>
            </q-td>
          </template>

          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="scenario_name" :props="props">
                {{
                  scenarios.length
                    ? currLang == "ru"
                      ? getScenario(props.row.scenario_id).description
                      : getScenario(props.row.scenario_id).description_kz
                    : ""
                }}
              </q-td>
              <q-td key="scenario_desc" :props="props">
                {{
                  scenarios.length
                    ? currLang == "ru"
                      ? props.row.scenario_desc
                      : props.row.scenario_desc_kz
                    : ""
                }}

                <q-tooltip>
                  {{
                    scenarios.length
                      ? currLang == "ru"
                        ? props.row.scenario_desc
                        : props.row.scenario_desc_kz
                      : ""
                  }}
                </q-tooltip>
              </q-td>

              <q-td key="score" :props="props">
                {{ props.row.score }}
              </q-td>
            </q-tr>
          </template>
        </q-table>

        <!-- <div class="row q-mt-md"> -->
        <!-- <span
          style="font-weight: 600;
font-size: 15px;
line-height: 28px;   font-family: 'Open Sans', sans-serif; color: #242D3C;"
        >
          {{ currentCase ? currentCase.event_id : "" }}</span
        > -->
        <!-- <q-badge
          class="q-ml-md"
          style="height: 20px; width: 20px;"
          color="green"
        /> -->
        <!-- <q-checkbox
          dense
          v-model="correctcasedetails"
          label="Выявлен корректно"
          color="teal"
        /> -->
        <!-- </div> -->

        <div class="q-mt-sm column q-gutter-y-sm" v-if="caseEventStatus == 2">
          <span class="text1"> {{ $t("workerComment") }}: </span>
          <q-input
            v-model="comment"
            flat
            outlined
            class="bg-white"
            :placeholder="$t('workerCommentPlaceholder')"
            autogrow
          />
          <div class="row q-mt-sm justify-end">
            <q-icon
              @click="uploadFiles('fileupload')"
              name="attachment"
              size="sm"
            />
            <input
              v-show="false"
              multiple
              type="file"
              :id="`fileupload`"
              :ref="`fileupload`"
              v-on:change="handleFilesUpload"
            />
            <span class="q-ml-md text1"> {{ $t("uploadDocument") }} </span>
          </div>
        </div>

        <div
          v-if="caseEventStatus == 2"
          :class="
            $q.screen.lt.md
              ? 'q-mt-md row items-center '
              : 'q-mt-md row items-center'
          "
        >
          <div
            :class="
              `row col-md q-pb-sm items-start q-gutter-sm ${
                $q.screen.lt.md ? '' : ''
              }`
            "
          >
            <q-card
              class="my-card col-5 col-md-2 row q-pa-none"
              v-for="(file, fileindex) in files"
              :key="fileindex"
              style="position: relative; height: 100px; width: 120px; "
            >
              <q-avatar
                v-if="!isImage(file)"
                font-size="12px"
                :style="{
                  'background-color': '#C6C6C6',
                  height: '100%',
                  width: '100%'
                }"
                class="col"
                text-color="white"
              >
                <a
                  rel="noopener noreferrer"
                  target="_blank"
                  download
                  style="line-height: 1.3; text-align: center"
                  :href="`/file-manager/api/v1/file/${file.id}`"
                >
                  {{ file.file_name }}
                </a>
              </q-avatar>

              <div v-else>
                <a
                  rel="noopener noreferrer"
                  target="_blank"
                  download
                  :href="`/file-manager/api/v1/file/${file.id}`"
                >
                  <img
                    style="width: 100px; height: 100px; background-position: center; background-size: contain; background-repeat: no-repeat;"
                    :src="`/file-manager/api/v1/file/${file.id}`"
                  />
                </a>
              </div>

              <q-icon
                @click="deleteFile(fileindex)"
                :disable="caseEventStatus !== 2"
                class="cursorstyle"
                name="close"
                color="white"
                size="15px"
                style="position: absolute; top:0; right: 0"
              />
            </q-card>
          </div>
        </div>
      </div>

      <q-dialog v-model="responsibleModal">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              {{ $t("changeAssignedUser") }}
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-card-section class="q-pt-none">
            <q-form>
              <q-list>
                <q-item class="q-pa-none">
                  <q-item-section>
                    <q-item-label> {{ $t("user") }} </q-item-label>
                    <q-select
                      dense
                      outlined
                      v-model="responsibleUser.user_id"
                      :options="users"
                      option-value="id"
                      :option-label="
                        opt =>
                          Object(opt) === opt
                            ? `${opt.first_name +
                                ' ' +
                                opt.last_name +
                                ' (' +
                                opt.email +
                                ')'}`
                            : ''
                      "
                      option-disable="inactive"
                      emit-value
                      map-options
                      class="q-mt-sm"
                    />
                  </q-item-section>
                </q-item>

                <q-item class="q-pa-none q-mt-md">
                  <q-item-section>
                    <q-item-label> {{ $t("dueDate") }} </q-item-label>
                    <q-input
                      dense
                      outlined
                      class="q-mt-sm"
                      v-model="responsibleUser.due_date"
                    >
                      <template v-slot:prepend>
                        <q-icon name="event" class="cursor-pointer">
                          <q-popup-proxy
                            transition-show="scale"
                            transition-hide="scale"
                          >
                            <q-date
                              v-model="responsibleUser.due_date"
                              :locale="myLocale"
                              mask="YYYY-MM-DD HH:mm"
                            >
                              <div class="row items-center justify-end">
                                <q-btn
                                  v-close-popup
                                  :label="$t('close')"
                                  color="primary"
                                  flat
                                />
                              </div>
                            </q-date>
                          </q-popup-proxy>
                        </q-icon>
                      </template>

                      <template v-slot:append>
                        <q-icon name="access_time" class="cursor-pointer">
                          <q-popup-proxy
                            transition-show="scale"
                            transition-hide="scale"
                          >
                            <q-time
                              v-model="responsibleUser.due_date"
                              mask="YYYY-MM-DD HH:mm"
                              format24h
                            >
                              <div class="row items-center justify-end">
                                <q-btn
                                  v-close-popup
                                  :label="$t('close')"
                                  color="primary"
                                  flat
                                />
                              </div>
                            </q-time>
                          </q-popup-proxy>
                        </q-icon>
                      </template>
                    </q-input>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('save')"
              :disabled="!responsibleUser.user_id || !responsibleUser.due_date"
              @click="editResponsible()"
              type="submit"
              color="primary"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-dialog v-model="comment_dialog">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              {{ $t("changeComment") }}
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-card-section class="q-pt-none">
            <q-form>
              <q-list>
                <q-item class="q-pa-none">
                  <q-item-section>
                    <q-input outlined v-model="editableComment.comment" />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('save')"
              @click="editComment(editableComment)"
              type="submit"
              color="primary"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-list class="q-mt-xs">
        <q-item
          class="q-mt-sm q-pa-sm"
          v-for="(comment, commentindex) in caseComments"
          :key="commentindex"
        >
          <q-input
            outlined
            class="col"
            :value="comment.comment"
            bottom-slots
            label-slot
          >
            <!-- <template v-slot:prepend>
          <q-avatar>
            <img src="https://cdn.quasar.dev/logo-v2/svg/logo.svg">
          </q-avatar>
        </template> -->

            <template v-slot:append>
              <q-icon
                size="xs"
                name="edit"
                color="darkgrey"
                style="cursor: pointer;"
                @click="onEditComment(comment)"
              >
              </q-icon>
              <q-icon
                size="xs"
                @click="deleteComment(comment)"
                name="delete"
                color="darkgrey"
                style="cursor: pointer;"
              >
              </q-icon>
            </template>

            <template v-slot:hint>
              {{ formatDate(comment.update_date) }}
            </template>

            <template v-slot:label>
              <span class="text-weight-bold text-deep-orange">
                {{
                  printUserName(comment.user_id) +
                    " " +
                    printLastName(comment.user_id) +
                    " (" +
                    printUserRole(comment.user_id) +
                    ")"
                }}
              </span>
            </template>
          </q-input>
        </q-item>
      </q-list>

      <div class="q-mt-lg">
        <span class="case_num_text">{{ $t("identificationDetails") }}</span>
      </div>
      <div
        class="cardcontainer column q-mt-lg bg-white q-px-md q-py-md q-gutter-y-sm"
      >
        <q-list class="rounded-borders">
          <div class="row items-center">
            <span class="text1"> {{ $t("iinbin") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.iin : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("isUserOrganizationClient") }}: </span>
            <span class="q-ml-xl text2">
              {{
                userDetails
                  ? userDetails.is_client
                    ? $t("yes")
                    : $t("no")
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("clientTypeForm") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.person_type : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("name") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.fname : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("patronymic") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.mname : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("lastname") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.lname : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("shortname") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.short_name : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("phoneNumber") }}: </span>
            <span class="q-ml-xl text2">
              {{ userDetails ? userDetails.mobile_number : "" }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("birthdateAndRegistration") }}: </span>
            <span class="q-ml-xl text2">
              {{
                userDetails
                  ? formattedDate(userDetails.reg_date, "DD:MM:YYYY HH:MM:SS")
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("login") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.reg_auth && caseEvents.reg_auth.login
                  ? caseEvents.reg_auth.login
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("mobileNumber") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.reg_auth && caseEvents.reg_auth.mob_number
                  ? caseEvents.reg_auth.mob_number
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("operationCreationDate") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper && caseEvents.finoper.oper_date_time
                  ? formattedDate(caseEvents.finoper.oper_date_time)
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("productId") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper && caseEvents.finoper.product_id
                  ? caseEvents.finoper.product_id
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("serviceId") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper && caseEvents.finoper.service_id
                  ? caseEvents.finoper.service_id
                  : ""
              }}
            </span>
          </div>
          <p class="q-mt-md q-mb-none text-bold">Отправитель</p>
          <!-- <div class="row items-center">
            <span class="text1"> {{ $t("currencyAmount") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? caseEvents.finoper.finoper_dc[0].amount
                  : ""
              }}
            </span>
          </div> -->
          <div class="row items-center">
            <span class="text1"> {{ $t("currencyAmountKzt") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? caseEvents.finoper.finoper_dc[0].amount_kzt
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardOwner") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.person && caseEvents.person[0]
                  ? caseEvents.person[0].full_name
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardNumberMasked") }}: </span>
            <span class="q-ml-xl text2 card-number-secure">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? maskCardNumber(caseEvents.finoper.finoper_dc[0].card_number)
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardOpenDate") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? formattedDate(
                      caseEvents.finoper.finoper_dc[0].card_open_date
                    )
                  : ""
              }}
            </span>
          </div>
          <!-- <div class="row items-center">
            <span class="text1"> {{ $t("cardExpDate") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? formattedDate(
                      caseEvents.finoper.finoper_dc[0].card_exp_date
                    )
                  : ""
              }}
            </span>
          </div> -->
          <p class="q-mt-md q-mb-none text-bold">Получатель</p>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardOwner") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.person && caseEvents.person[1]
                  ? caseEvents.person[1].full_name
                  : ""
              }}
            </span>
          </div>

          <div class="row items-center">
            <span class="text1"> {{ $t("mobileNumber") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.person && caseEvents.person[1]
                  ? caseEvents.person[1].mobile_number
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardNumberMasked") }}: </span>
            <span class="q-ml-xl text2 card-number-secure">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length > 1
                  ? maskCardNumber(caseEvents.finoper.finoper_dc[1].card_number)
                  : ""
              }}
            </span>
          </div>
          <div class="row items-center">
            <span class="text1"> {{ $t("cardOpenDate") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? formattedDate(
                      caseEvents.finoper.finoper_dc[1].card_open_date
                    )
                  : ""
              }}
            </span>
          </div>
          <!-- <div class="row items-center">
            <span class="text1"> {{ $t("cardExpDate") }}: </span>
            <span class="q-ml-xl text2">
              {{
                caseEvents.finoper &&
                caseEvents.finoper.finoper_dc &&
                caseEvents.finoper.finoper_dc.length
                  ? formattedDate(
                      caseEvents.finoper.finoper_dc[1].card_exp_date
                    )
                  : ""
              }}
            </span>
          </div> -->

          <!--
            <div class="row items-center">
              <span class="text1"> {{$t('userSystemId')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  userDetails ? userDetails.bs_id : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1">
                {{$t('clientRequestProcessed')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                                    userDetails ? userDetails.client_reg_date : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1">
                {{$t('clientRole')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                  userDetails ? userDetails.person_role : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('orgLegalForm')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                                    userDetails ? userDetails.opf : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1">
                {{$t('documentType')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                                            userDetails ? userDetails.doc_type : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('documentNumber')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                   userDetails ? userDetails.doc_number : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1">
                {{$t('citizenship')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                                                                                userDetails ? userDetails.cit_country : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('countryResidence')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                 userDetails ? userDetails.res_country : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('countryBirthAndRegistration')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                 userDetails ? userDetails.reg_country : ""
                }}
              </span>
            </div>




            <div class="row items-center">
              <span class="text1"> {{$t('error')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.reg_auth && caseEvents.reg_auth.err_code
                    ? caseEvents.reg_auth.err_code
                    : ""
                }}
              </span>
            </div>



            <div class="row items-center">
              <span class="text1"> {{$t('uniqueOperationId')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.oper_id
                    ? caseEvents.finoper.oper_id
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('operationType')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.oper_sub_type
                    ? caseEvents.finoper.oper_type
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('operationSubType')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.oper_sub_type
                    ? caseEvents.finoper.oper_sub_type
                    : ""
                }}
              </span>
            </div>



            <div class="row items-center">
              <span class="text1"> {{$t('knpCode')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.knp_code
                    ? caseEvents.finoper.knp_code
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('kbkCode')}}: </span>
              <span class="q-ml-xl text2"
                >{{
                  caseEvents.finoper && caseEvents.finoper.kbk_code
                    ? caseEvents.finoper.kbk_code
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('knoCode')}}: </span>
              <span class="q-ml-xl text2"
                >{{
                  caseEvents.finoper && caseEvents.finoper.knp_code
                    ? caseEvents.finoper.knp_code
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('productGroupId')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.product_grp_id
                    ? caseEvents.finoper.product_grp_id
                    : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1"> {{$t('paymentAssignment')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.reason
                    ? caseEvents.finoper.reason
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('openRefCode')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper && caseEvents.finoper.ref_oper_id
                    ? caseEvents.finoper.ref_oper_id
                    : ""
                }}
              </span>
            </div>



            <div class="row items-center">
              <span class="text1"> {{$t('currencyCode')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].currency_code
                    : ""
                }}
              </span>
            </div>
-->
          <!-- <div class="row items-center">
              what is bs account
              <span class="text1"> IBAN: </span>
              <span class="q-ml-xl text2"> {{caseEvents.finoper && caseEvents.finoper.finoper_dc.length ? caseEvents.finoper.finoper_dc[0].amount_kzt : ""}}  </span>
            </div> -->

          <!--
            <div class="row items-center">
              <span class="text1">
                {{$t('debitCredit')}}:
              </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].oper_dc
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('mcc')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].mcc
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('cardType')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].card_type
                    : ""
                }}
              </span>
            </div>

            <div class="row items-center">
              <span class="text1"> {{$t('bankBic')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bank_bic
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('bankName')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bank_name
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('gkAccount')}}: </span>
              <span class="q-ml-xl text2">
                {{
                  caseEvents.finoper &&
                  caseEvents.finoper.finoper_dc &&
                  caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bs_account
                    : ""
                }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1"> {{$t('serviceOperatorCode')}}: </span>
              <span class="q-ml-xl text2">-->
          <!-- {{
                  caseEvents.finoper && caseEvents.finoper.finoper_dc && caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bank_name
                    : ""
                }} -->
          <!-- </span>
            </div>-->
          <!--<div class="row items-center">
              <span class="text1"> {{$t('fiscalType')}}: </span>
              <span class="q-ml-xl text2">-->
          <!-- {{
                  caseEvents.finoper && caseEvents.finoper.finoper_dc && caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bank_name
                    : ""
                }} -->
          <!-- </span>
            </div>-->
          <!--  <div class="row items-center">
              <span class="text1"> {{$t('fiscalNumber')}}: </span>
              <span class="q-ml-xl text2"> -->
          <!-- {{
                  caseEvents.finoper && caseEvents.finoper.finoper_dc && caseEvents.finoper.finoper_dc.length
                    ? caseEvents.finoper.finoper_dc[0].bank_name
                    : ""
                }} -->
          <!-- </span>
            </div>  -->

          <!--
            <div class="row items-center">
              <span class="text1">
                {{$t('browserSessionId')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.session_id ? caseEvents.session_id : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('userAgentInfo')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.user_agent ? caseEvents.user_agent : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('ip4Client')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.ip_addr ? caseEvents.ip_addr : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('ip6Client')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.ip_addr_v6 ? caseEvents.ip_addr_v6 : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('physicalAddressClient')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.mac_address ? caseEvents.mac_address : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('deviceImei')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.dev_id ? caseEvents.dev_id : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('country')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.country_id ? caseEvents.country_id : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('region')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.region_id ? caseEvents.region_id : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('city')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.city_id ? caseEvents.city_id : "" }}
              </span>
            </div>
            <div class="row items-center">
              <span class="text1">
                {{$t('zip')}}:
              </span>
              <span class="q-ml-xl text2">
                {{ caseEvents.post_index ? caseEvents.post_index : "" }}
              </span>
            </div> -->
        </q-list>

        <!-- <div class="q-mt-lg q-ml-sm">
        <span class="showmore_text"> Показать еще 10 (всего 6) </span>
      </div> -->

        <!-- <q-checkbox
        size="xs"
        v-model="correctcasedetails"
        label="Подтвердить корректность выявления"
        class="text1"
      /> -->

        <div class="q-mt-lg row items-center">
          <span class="text1"> {{ $t("chooseStatus") }}: </span>
          <!--           :disable="currentCase.decision !== 0"
 -->
          <q-select
            class="q-ml-md"
            outlined
            rounded
            dense
            v-model="casestatusSelect"
            :options="statuses"
            :option-label="
              opt => (opt.key == '1' ? $t('allowed') : $t('forbidden'))
            "
            option-value="key"
            emit-value
            map-options
            style="min-width: 250px; max-width: 300px"
          >
          </q-select>
        </div>

        <div class="row q-gutter-x-lg q-gutter-y-md q-mt-lg">
          <q-btn class="button1" @click="saveStatus" :label="$t('save')" />
          <q-btn
            class="button2"
            @click="$router.push({ name: 'cases' })"
            :label="$t('close')"
          />
        </div>
      </div>
      <div class="q-mt-md">
        <CustomTable
          :data="related_transfers"
          :columns="relatedEvents"
          :tabletitle="$t('customer_transactions')"
          :isDeleteAvailable="false"
          :isAddAvailable="false"
          :isEditAvailable="false"
          :pagination.sync="pagination"
          @onRequest="onRequest"
          :loading="loading"
          :filter.sync="filter"
        >
          <template v-slot:searchfield="{ props }">
            <q-input
              outlined
              dense
              debounce="300"
              v-model="filter"
              :placeholder="$t('search')"
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>
            <span class="q-ml-lg col-1 q-mr-sm"> {{ $t("period") }}: </span>
            <q-input outlined dense type="date" v-model="startDate" />
            <q-input outlined dense type="date" v-model="endDate" />
          </template>

          <template v-slot:oper_date_time="{ props }">
            {{ formattedDate(props.row.oper_date_time) }}
          </template>
          <template v-slot:person_dc="{ props }">
            <q-icon
              v-if="props.row.person_dc === 1"
              name="arrow_upward"
              size="20px"
              color="red"
            />
            <q-icon
              v-else-if="props.row.person_dc === 2"
              name="arrow_downward"
              size="20px"
              color="green"
            />
            <span v-else>{{ props.row.person_dc }}</span>
          </template>
          <template v-slot:card_number="{ props }">
            {{ maskCardNumber(props.row.card_number) }}
          </template>
          <template v-slot:transaction_status="{ props }">
            <q-icon
              v-if="props.row.transaction_status === 1"
              name="check_circle"
              size="20px"
              color="green"
            />
            <q-icon
              v-else-if="props.row.transaction_status === 3"
              name="cancel"
              size="20px"
              color="red"
            />
            <span v-else>{{ props.row.transaction_status }}</span>
          </template>
        </CustomTable>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import { date } from "quasar";
import axios from "axios";
import datemixin from "../mixins/datemixin";
import { maskCardNumber } from "../utils/cardMask";

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable")
  },
  props: {
    caseidprop: {
      type: Number,
      default: 0
    },
    returnbuttonneeded: {
      type: Boolean,
      default: true
    }
  },
  created() {
    if (!this.scenarios.length) {
      this.getAllScenarios({
        page: 1,
        per_page: 100
      })
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    if (!this.users.length) {
      this.getAllUsers()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    this.getCaseComments(this.caseid)
      .then(res => {})
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });

    if (!this.caseStatusesSystem.length) {
      this.getCaseStatusesSystem({ page: 1, per_page: 10 })
        .then(res => {})
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }

    if (!this.statuses.length) {
      this.getCaseStatuses({ page: 1, per_page: 10 })
        .then(res => {})
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
    this.getCaseById({
      page: 1,
      per_page: 1,
      sort_field: "id",
      sort_order: "asc",
      filter_fields: "id",
      filter_values: this.caseid
    }).then(result => {
      if (this.currentCase.decision !== 0) {
        this.casestatusSelect = "" + this.currentCase.decision;
      }
      this.currentCase.case_files.forEach(file => {
        this.getFileInfoByFileId(file.file_id)
          .then(response => {
            this.files.push(response);
          })
          .catch(err => {
            this.$q.notify({
              message: "error retrieving files",
              color: "negative"
            });
          });
      });
      this.comment = "";
    });

    this.getCaseEvents(this.caseid).then(result => {
      if (this.caseEvents.person[0].iin != "") {
        this.personIIN = this.caseEvents.person[0].iin;
        this.onRequest({ pagination: this.pagination, filter: this.filter });
      }
    })
    .catch(err => {
      this.$q.notify({
        message: "record not found",
        color: "negative"
      });
    });
  },

  data() {
    return {
      responsibleUser: {
        user_id: "",
        due_date: this.formatDate(Date.now(), "YYYY-MM-DD HH:MM")
      },
      isOpen: true,
      loading: false,
      comment_dialog: false,
      responsibleModal: false,
      editableComment: {},
      casestatus: "",
      comment: "",
      correctcasedetails: false,
      casestatusSelect: "",
      files: [],

      mode: "list",
      selectedValue: "",
      selectedFilter: "",
      pagination: {
        sortBy: "oper_date_time",
        descending: true,
        page: 1,
        rowsPerPage: 10,
        rowsNumber: 200
      },
      filter: "",
      startDate: "",
      endDate: "",
      personIIN: ""
    };
  },

  watch: {
    selectedFilter: function(newval, oldval) {
      if (newval !== oldval) {
        this.selectedValue = "";
      }
      if (!newval) {
        const filter = this.filter;
        const pagination = this.pagination;
        this.onRequest({ filter, pagination });
      }
    },
    startDate(newVal) {
      this.onRequest({ pagination: this.pagination, filter: this.filter });
    },
    endDate(newVal) {
      this.onRequest({ pagination: this.pagination, filter: this.filter });
    },
    caseidprop: function(newval, oldval) {
      if (newval !== oldval && newval) {
        this.getCaseComments(newval)
          .then(res => {})
          .catch(err => {
            this.$q.notify({
              message: err.error,
              color: "negative"
            });
          });

        this.getCaseById({
          page: 1,
          per_page: 1,
          sort_field: "id",
          sort_order: "asc",
          filter_fields: "id",
          filter_values: newval
        }).then(result => {
          if (this.currentCase.decision !== 0) {
            this.casestatusSelect = "" + this.currentCase.decision;
          }
          this.files = [];
          this.currentCase.case_files.forEach(file => {
            this.getFileInfoByFileId(file.file_id)
              .then(response => {
                this.files.push(response);
              })
              .catch(err => {
                this.$q.notify({
                  message: "error retrieving files",
                  color: "negative"
                });
              });
          });
          this.comment = "";
        });

        this.getCaseEvents(newval)
          .then(result => {})
          .catch(err => {
            this.$q.notify({
              message: "record not found",
              color: "negative"
            });
          });
      }
    }
  },

  computed: {
    ...mapState({
      related_transfers: state => state.cases.related_transfers,
      caseEvents: state => state.cases.caseEvents,
      caseEventStatus: state => state.cases.caseEventStatus,
      caseStatusesSystem: state => state.admin.statusessystem,
      caseComments: state => state.cases.caseComments,
      currentCase: state => state.cases.case,
      statuses: state => state.admin.statuses,
      scenarios: state => state.admin.scenarios,
      users: state => state.admin.users,
      data() {
        return this.currentCase && this.currentCase.case_details
          ? this.currentCase.case_details
          : [];
      },

      myLocale() {
        return {
          /* starting with Sunday */
          days: [
            this.$t("sunday"),
            this.$t("monday"),
            this.$t("tuesday"),
            this.$t("wednesday"),
            this.$t("thursday"),
            this.$t("friday"),
            this.$t("saturday")
          ],
          daysShort: [
            this.$t("sundayShort"),
            this.$t("mondayShort"),
            this.$t("tuesdayShort"),
            this.$t("wednesdayShort"),
            this.$t("thursdayShort"),
            this.$t("fridayShort"),
            this.$t("saturdayShort")
          ],
          months: [
            this.$t("january"),
            this.$t("february"),
            this.$t("march"),
            this.$t("april"),
            this.$t("may"),
            this.$t("june"),
            this.$t("july"),
            this.$t("august"),
            this.$t("september"),
            this.$t("october"),
            this.$t("november"),
            this.$t("december")
          ],
          monthsShort: [
            this.$t("janShort"),
            this.$t("febShort"),
            this.$t("marchShort"),
            this.$t("aprilShort"),
            this.$t("mayShort"),
            this.$t("juneShort"),
            this.$t("julyShort"),
            this.$t("augustShort"),
            this.$t("septemberShort"),
            this.$t("octoberShort"),
            this.$t("novemberShort"),
            this.$t("decemberShort")
          ],
          firstDayOfWeek: 1
        };
      },

      userDetails() {
        let person = null;
        if (
          this.caseEvents.finoper &&
          this.caseEvents.finoper.person &&
          this.caseEvents.finoper.person.length
        ) {
          person = this.caseEvents.finoper.person[0];

          return person;
        } else if (
          this.caseEvents &&
          this.caseEvents.person &&
          this.caseEvents.person.length
        ) {
          person = this.caseEvents.person[0];
          return person;
        } else {
          return person;
        }
      },
      caseid() {
        return this.caseidprop ? this.caseidprop : this.$route.params.case_id;
      },
      columns() {
        return [
          {
            name: "scenario_name",
            label: this.$t("columnName"),
            align: "left",
            field: "scenario_name",
            style: "width: 60%; white-space: normal;",
            sortable: true
          },
          {
            name: "scenario_desc",
            label: this.$t("columnDescription"),
            style: "width: 30%; white-space: normal;",
            align: "left",
            field: "scenario_desc",
            sortable: true
          },
          {
            name: "score",
            label: this.$t("columnScore"),
            align: "left",
            field: "score",
            sortable: true
          }
        ];
      },

      relatedEvents() {
        return [
          {
            name: "oper_date_time",
            align: "left",
            label: "Дата",
            field: "oper_date_time",
            sortable: true
          },

          {
            name: "product_id",
            label: "Product ID",
            align: "left",
            field: "product_id",
            sortable: true
          },

          {
            name: "person_dc",
            label: "Дебит/Кредит",
            align: "left",
            field: "person_dc",
            sortable: true
          },
          {
            name: "iin",
            label: "ИИН",
            align: "left",
            field: "iin",
            sortable: true
          },

          {
            name: "amount_kzt",
            align: "left",
            label: "Сумма (KZT)",
            field: "amount_kzt",
            sortable: true
          },

          {
            name: "card_number",
            label: "Номер карты",
            align: "left",
            field: row => maskCardNumber(row.card_number),
            sortable: true
          },
          {
            name: "transaction_status",
            align: "left",
            label: "Статус",
            field: "transaction_status",
            sortable: true
          },
          {
            name: "actions",
            align: "left",
            label: "",
            field: "actions",
            sortable: false
          }
        ];
      }
      // caseEventStatus() {
      //   return this.currentCase && Object.keys(this.currentCase).length ? this.currentCase.decision : 0
      // }
    })
  },
  methods: {
    ...mapActions({
      deleteCaseComment: "cases/deleteComment",
      getAllUsers: "admin/getAllUsers",
      getCaseEvents: "cases/getCaseEvents",
      getCaseById: "cases/getCaseById",
      getCaseStatuses: "admin/getCaseStatuses",
      getCaseStatusesSystem: "admin/getCaseStatusesSystem",
      getFileInfoByFileId: "cases/getFileInfoByFileId",
      addCaseComment: "cases/addComment",
      deleteFileById: "cases/deleteFileById",
      updateCase: "cases/updateCase",
      getCaseComments: "cases/getCaseComments",
      editCaseComment: "cases/editCaseComment",
      changeCaseAssignee: "cases/changeCaseAssignee",
      getAllScenarios: "admin/getAllScenarios",
      getAllRelatedTransfers: "cases/getAllRelatedTransfers"
    }),

    // Используем импортированную утилиту для маскирования номера карты
    maskCardNumber,

    convertCaseStatusSystemVal(val) {
      if (val == "Запрещено") {
        return this.$t("forbidden");
      } else if (val == "Разрешено") {
        return this.$t("allowed");
      } else if (val == "Приостановлено") {
        return this.$t("suspended");
      } else if (val == "На анализе") {
        return this.$t("onanalys");
      } else {
        return this.$t("unabletocheck");
      }
    },

    getScenario(id) {
      let scenario = this.scenarios.find(x => x.id == id);
      return scenario;
    },

    editResponsible() {
      const form = {
        ...this.currentCase,
        finish_assigned_date: this.formatDateToDB(
          this.responsibleUser.due_date
        ),
        assigned_user: +this.responsibleUser.user_id,
        start_assigned_date: this.formatDateToDB(new Date())
      };
      this.changeCaseAssignee(form)
        .then(() => {
          this.$q.notify({
            message: "Вы успешно изменили срок и ответсвенного пользователя",
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    onChangeResponsible() {
      this.responsibleUser = {
        user_id: this.currentCase.assigned_user || "",
        due_date: this.formatDateDefault(Date.now())
      };
      this.responsibleModal = true;
    },

    onEditComment(comment) {
      this.comment_dialog = true;
      this.editableComment = { ...comment };
    },

    deleteComment(comment) {
      this.deleteCaseComment(comment.id)
        .then(() => {
          this.$q.notify({
            message: "Коммент успешно удален  ",
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    editComment(comment) {
      const form = {
        ...comment,
        update_date: this.formatDateToDB(new Date())
      };

      this.editCaseComment(form)
        .then(() => {
          this.$q.notify({
            message: "Коммент успешно обновлен",
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    deleteFile(index) {
      this.loading = true;
      this.deleteFileById(this.files[index].id)
        .then(result => {
          let case_files = [];

          for (let i = 0; i < this.files.length; i += 1) {
            if (this.files[i].id !== this.files[index].id) {
              case_files.push({
                case_id: this.currentCase.id,
                file_id: this.files[i].id
              });
            }
          }

          const form = { ...this.currentCase, case_files };
          this.updateCase(form)
            .then(() => {
              this.files.splice(index, 1);
            })
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
          this.loading = false;
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
          this.loading = false;
        });
    },
    parseFileType(type) {
      let finaltype = type.split("/")[1];
      return finaltype;
    },

    isImage(file) {
      if (
        file.file_extension == "png" ||
        file.file_extension == "jpg" ||
        file.file_extension == "jpeg"
      ) {
        return true;
      }
      return false;
    },
    uploadFiles(type) {
      this.$refs[type].click();
    },
    async handleFilesUpload() {
      let uploadedFiles = this.$refs["fileupload"].files;
      let formData = new FormData();
      let self = this;
      for (let i = 0; i < uploadedFiles.length; i += 1) {
        const file = uploadedFiles[i];
        formData.append("files", file);
      }
      this.loading = true;
      axios
        .post("/file-manager/api/v1/file", formData, {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        })
        .then(function(result) {
          const fileRes = result.data.result;
          self.files = [...self.files, ...fileRes];
          self.loading = false;
        })
        .catch(function() {
          self.$q.notify({
            message: "ошибка при загрузке файла",
            color: "negative"
          });
          self.loading = false;
        });
    },
    saveStatus() {
      if (!this.comment.length) {
        this.$q.notify({
          message: "Введите комментарий оператора",
          color: "negative"
        });
        return;
      }

      let case_files = [];

      for (let i = 0; i < this.files.length; i += 1) {
        case_files.push({
          case_id: this.currentCase.id,
          file_id: this.files[i].id
        });
      }

      const commentForm = {
        case_id: this.currentCase.id,
        comment: this.comment,
        is_active: true,
        user_id: this.user.id
      };

      const form = {
        ...this.currentCase,
        decision: +this.casestatusSelect,
        is_confirmed: true,
        comment: this.comment,
        case_files
      };

      this.updateCase(form)
        .then(() => {
          this.addCaseComment(commentForm)
            .then(res => {
              this.comment = "";
            })
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });

          this.$q.notify({
            message: "Кейс успешно сохранен",
            color: "green"
          });
          this.$router.push({ name: "cases" });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },
    printCaseRisk(score) {
      if (score <= 40) {
        return "Низкий";
      } else if (score > 40 && score <= 60) {
        return "Средний";
      }
      return "Высокий";
    },
    formattedDate(val) {
      let newdate = date.subtractFromDate(val, { hours: 5 });
      return date.formatDate(newdate, "YYYY-MM-DD HH:mm:ss");
    },
    exportTable() {
      // naive encoding to csv format
      const content = [this.columns.map(col => wrapCsvValue(col.label))]
        .concat(
          this.data.map(row =>
            this.columns
              .map(col =>
                wrapCsvValue(
                  typeof col.field === "function"
                    ? col.field(row)
                    : row[col.field === void 0 ? col.name : col.field],
                  col.format
                )
              )
              .join(",")
          )
        )
        .join("\r\n");

      const status = exportFile("quotes.csv", content, "text/csv");

      if (status !== true) {
        this.$q.notify({
          message: "Browser denied file download...",
          color: "negative",
          icon: "warning"
        });
      }
    },
    async onRequest(
      props,
      filter_field = ["ep.iin", "efo.product_id", "efod.card_number"],
      filter_value
    ) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;
      let start_date = this.startDate;
      let end_date = this.endDate;
      let iin;
      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;
      if(this.caseEvents&&this.caseEvents.person){
        iin=this.caseEvents.person[0].iin

        this.pagination.rowsNumber = await this.getAllRelatedTransfers({
          page: page,
          per_page: rowsPerPage,
          sort_field: sortBy,
          sort_order: descending ? "desc" : "asc",
          filter_fields: filter_field,
          filter_values: filter_value
            ? [filter_value, filter_value, filter_value]
            : [filter, filter, filter],
          start_date: start_date,
          end_date: end_date,
          iin: iin
        });
      }
      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
<style>
.q-chip__content {
  display: block;
  text-align: center;
}
.arrow_back_style {
  background: rgba(36, 45, 60, 0.04);
  border-radius: 8px;
  height: 40px;
  width: 40px;
}
.arrow_back_text {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 26px;
  /* identical to box height, or 162% */

  letter-spacing: -0.355556px;
  cursor: pointer;
  /* Basic / Black */

  color: #12022f;
}

.case_num_text {
  font-style: normal;
  font-weight: bold;
  font-size: 23px;
  line-height: 34px;
  /* identical to box height, or 142% */

  letter-spacing: -0.8px;

  /* Basic / Black */

  color: rgb(3, 35, 65);
}

.showmore_text {
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 26px;
  color: #d2ab69;
}

.text1 {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: rgba(36, 45, 80, 0.7);
}

.text2 {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  /* identical to box height, or 162% */
  color: #12022f;
}

.expansion_item {
  background: rgba(232, 234, 242, 0.64);
  border-radius: 8px;
  height: 65px;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 26px;
  color: #12022f;
}

.button1 {
  background: #d2ab66;
  border-radius: 4px;
  height: 42px;
  color: white;
  width: 265px;
  font-style: normal;
  font-weight: normal;
}

.button2 {
  height: 42px;
  background: #17335d;
  border-radius: 4px;
  color: white;
  width: 265px;
  font-style: normal;
  font-weight: normal;
}

.cardcontainer {
  border-radius: 16px;
  box-shadow: 0 5px 10px rgba(154, 160, 185, 0.05),
    0 15px 40px rgba(166, 173, 201, 0.2);
}
.my-card {
  width: 100%;
  max-width: 250px;
}

/* Дополнительная безопасность для номеров карт */
.card-number-secure {
  /* Предотвращаем выделение текста */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  /* Предотвращаем копирование через контекстное меню */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}
</style>
