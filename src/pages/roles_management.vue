<template>
  <RoleTable :roles="roles" @addNewRole="addNewRoleInner" @deleteRole="deleteRoleInner" @editRoleInner="editRoleInner"/>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    RoleTable: () => import("../components/RoleTable")
  },

  created() {
    if (!this.roles.length) {
      this.getAllRoles()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  },

  computed: mapState({
    roles: state => state.admin.roles
  }),

  methods: {
    ...mapActions({
      getAllRoles: "admin/getAllRoles",
      addNewRole: "admin/addNewRole",
      deleteRole: "admin/deleteRole",
      editRole: "admin/editRole"
    }),
    editRoleInner(role) {
      this.editRole(role).then().catch(err => {
            this.$q.notify({
            message: err.message,
            color: "negative"
          });
       }) 
    },

    addNewRoleInner(role) {
       this.addNewRole(role).then(response => {
            this.$q.notify({
            message: `Роль ${role.name_ru} успешно создана`,
            color: 'green'
        })
       }).catch(err => {
            this.$q.notify({
            message: err.message,
            color: "negative"
          });
       }) 
    },

     deleteRoleInner(role) {
   this.deleteRole(role.id).then(response => {
            this.$q.notify({
            message: `Роль ${role.name_ru} успешно удалена`,
            color: 'green'
        })
       }).catch(err => {
            this.$q.notify({
            message: err.message,
            color: "negative"
          });
       })  
  }
  }
};
</script>
