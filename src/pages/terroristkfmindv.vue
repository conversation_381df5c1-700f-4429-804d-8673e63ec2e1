<template>
  <CustomTable
    :data="terroristKfmIndv"
    :columns="columns"
    :tabletitle="$t('terroristKfmIndv')"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :isEditAvailable="false"
    :pagination.sync="pagination"
    :loading="loading"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      pagination: {
        sortBy: "birthdate",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      terroristKfmIndv: state => state.admin.terroristKfmIndv
    }),
    columns() {
      return [{
          name: "birthdate",
          label: this.$t('birthdate'),
          field: row => row.birthdate,
          sortable: true,
          align: "center"
        },
        {
          name: "fname",
          label: this.$t('firstName'),
          field: row => row.fname,
          align: "center",
          sortable: true
        },
        {
          name: "fname_en",
          label: this.$t('firstNameEng'),
          field: row => row.fname_en,
          align: "center",
          sortable: true
        },
        {
          name: "iin",
          label: this.$t('iin'),
          field: row => row.iin,
          align: "center",
          sortable: true
        },
        {
          name: "lname",
          label: this.$t('lastname'),
          field: row => row.lname,
          align: "center",
          sortable: true
        },
        {
          name: "lname_en",
          label: this.$t('lastnameEng'),
          field: row => row.lname_en,
          align: "center",
          sortable: true
        },
        {
          name: "mname",
          label: this.$t('middleName'),
          field: row => row.mname,
          align: "center",
          sortable: true
        },
        {
          name: "mname_en",
          label: this.$t('middleNameEng'),
          field: row => row.mname_en,
          align: "center",
          sortable: true
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.note,
          align: "center"
        },
        {
          name: "num",
          label: this.$t('number'),
          field: row => row.num,
          align: "center",
          sortable: true
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }]
    }
  },

  methods: {
    ...mapActions({
      getTerroristsKfmIndv: "admin/getTerroristsKfmIndv"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getTerroristsKfmIndv({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "birthdate,fname,fname_en,iin,lname,lname_en,mname,mname_en,num"
        ),
        filter_values:
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter +
          "," +
          filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
