<template>
  <CustomTable
    :data="emailNotifications"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editScoringItem"
    @addRow="addScoringItem"
    :tabletitle="$t('notificationSettings')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="true"
    :is_edit_disabled="!emailNotification.email"
    :is_add_disabled="!emailNotification.email"
    :isAddAvailable="true"
    :loading="loading"
    :filter.sync="filter"
    @deleteRow="deleteScoringItem"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>


    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('email')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="emailNotification.email"
                  outlined
                  type="text"
                  :label="$t('email')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('addValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
             <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">
                  {{$t('email')}}
                </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="emailNotification.email"
                  outlined
                  type="text"
                  :label="$t('email')"
                />
              </q-item-section>
            </q-item>

          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      emailNotification: {
       email: ""
      },
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  created() {
    
    this.getEmailNotifications()
      .then(response => {})
      .catch(err => {
        this.$q.notify({
          message: err.message,
          color: "negative"
        });
      });
  },

  computed: {
    ...mapState({
      emailNotifications: state => state.admin.emailNotifications,
    }),
     columns() { 
       return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
           name: "email",
          label: this.$t('email'),
          field: row => row.email,
          sortable: true,
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
  }},

  methods: {
    ...mapActions({
      editEmailNotification: "admin/editEmailNotification",
      getEmailNotifications: "admin/getEmailNotifications",
      deleteEmailNotification: "admin/deleteEmailNotification",
      addEmailNotification: "admin/addEmailNotification",
    }),

    addScoringItem() {
      let form = {...this.emailNotification};
      this.addEmailNotification(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно добавлено`,
            color: "green"
          });
          this.reset()
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    editScoringItem() {
      let form = {...this.emailNotification};
      this.editEmailNotification(form)
        .then(response => {
          this.$q.notify({
            message: `Значение успешно изменено `,
            color: "green"
          });
          this.reset()
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    deleteScoringItem(row) {
      this.deleteEmailNotification(row.id)
        .then(response => {
          this.$q.notify({
            message: `Значение ${row.id} удалено`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getEmailNotifications({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("id"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.emailNotification = {
        ...row
      };
    },

    reset() {
      this.emailNotification = {
        email: "",
      };
    }
  }
};
</script>
