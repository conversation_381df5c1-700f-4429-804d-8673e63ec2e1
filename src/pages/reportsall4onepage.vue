<template>
  <q-page class="q-pa-lg column">
    <div class="col column" style="height: 80vh">
      <div class="row col q-pa-none q-pl-md q-pr-md justify-between">
        <div
          class="cardcontainer col"
          style="max-height: -webkit-fill-available; "
        >
          <Bar
            :chartData="report9datasets"
            :options="report1options"
            :styles="{ height: '100%' }"
          />
        </div>
        <div
          class="cardcontainer col q-ml-md"
          style="max-height: -webkit-fill-available; "
        >
          <Bar
            :chartData="report4datasets"
            :options="report1options"
            :styles="{ height: '100%' }"
          />
        </div>
      </div>

      <div
        class="row col onerow q-pa-none q-pl-md q-pr-md q-mt-md justify-between "
      >
        <div
          class="cardcontainer col"
          style="max-height: -webkit-fill-available; "
        >
          <Bar
            :chartData="report2datasets"
            :options="report1options"
            :styles="{ height: '100%' }"
          />
        </div>
        <div
          class="cardcontainer col q-ml-md "
          style="max-height: -webkit-fill-available; "
        >
          <Bar
            :chartData="report1datasets"
            :options="report1options"
            :styles="{ height: '100%' }"
          />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";
import Bar from "../components/BarChart";
import datemixin from "../mixins/datemixin";

export default {
  mixins: [datemixin],
  components: {
    Bar: Bar
  },

  data() {
    return {
      report1options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          xAxes: [
            {
              stacked: true,
              categoryPercentage: 0.5,
              barPercentage: 1
            }
          ],
          yAxes: [
            {
              stacked: true
            }
          ]
        }
      }
    };
  },

  created() {
    let start_date = new Date();
    start_date.setHours(0, 0, 0, 0);
    start_date = this.formatDateToDB(start_date);
    let end_date = this.formatDateToDB(new Date());

    this.getReport9({ start_date, end_date })
      .then(() => {
        console.log(this.report9);
      })
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });

    this.getReport1({ start_date, end_date })
      .then(() => {
        console.log(this.report9);
      })
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });

    this.getReport2({ start_date, end_date })
      .then()
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });

    this.getReport3({ start_date, end_date })
      .then()
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });
  },
  computed: mapState({
    report9: state => state.admin.report9,
    report2: state => state.admin.report2,
    report3: state => state.admin.report3,
    report1: state => state.admin.report1,
    report1datasets() {
      const report1labels = [
        `${this.$t("registration")} ${this.$t("fl")}`,
        `${this.$t("registration")} ${this.$t("ep")}`,
        `${this.$t("registration")} ${this.$t("ul")}`,
        `${this.$t("authorization")} ${this.$t("fl")}`,
        `${this.$t("authorization")} ${this.$t("ep")}`,
        `${this.$t("authorization")} ${this.$t("ul")}`,
        `${this.$t("finShortcaseCount")} ${this.$t("fl")}`,
        `${this.$t("finShortcaseCount")} ${this.$t("ep")}`,
        `${this.$t("finShortcaseCount")} ${this.$t("ul")}`
      ];

      if (this.report1 && this.report1.cases) {
        const cases = this.report1.cases;
        const events = this.report1.events;

        const datasets = [
          {
            label: this.$t("caseCount"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              cases.registration_fl,
              cases.registration_ip,
              cases.registration_ul,
              cases.auth_fl,
              cases.auth_ip,
              cases.auth_ul,
              cases.fin_oper_fl,
              cases.fin_oper_ip,
              cases.fin_oper_ul
            ]
          },
          {
            label: this.$t("eventsAmount"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events.registration_fl,
              events.registration_ip,
              events.registration_ul,
              events.auth_fl,
              events.auth_ip,
              events.auth_ul,
              events.fin_oper_fl,
              events.fin_oper_ip,
              events.fin_oper_ul
            ]
          }
        ];

        return {
          labels: report1labels,
          datasets
        };
      }

      return {
        labels: report1labels,
        datasets: []
      };
    },
    report2datasets() {
      const report2labels = [
        `${this.$t("eventsAmountShort")} Регистрация`,
        `${this.$t("eventsAmountShort")} Авторизация`,
        `${this.$t("eventsAmountShort")} Фин операция`,
        `${this.$t("caseCountShort")} Регистрация`,
        `${this.$t("caseCountShort")} Авторизация`,
        `${this.$t("caseCountShort")} Фин операция`
      ];
      if (this.report3 && this.report3.cases) {
        const cases = this.report3.cases;
        const events = this.report3.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          datasets.push({
            label: cases[i].country_name,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events[i].registration,
              events[i].auth,
              events[i].fin_oper,
              cases[i].registration,
              cases[i].auth,
              cases[i].fin_oper
            ]
          });
        }

        return {
          labels: report2labels,
          datasets
        };
      }

      return {
        labels: report2labels,
        datasets: []
      };
    },

    report9datasets() {
      const report9labels = [
        this.$t("allowed"),
        this.$t("suspended"),
        this.$t("forbidden")
      ];

      if (this.report9) {
        const report9 = this.report9;

        const datasets = [
          {
            label: this.$t("authorization"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.auth_allowed,
              report9.auth_suspended,
              report9.auth_forbidden
            ]
          },
          {
            label: this.$t("registration"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.registration_allowed,
              report9.registration_suspended,
              report9.registration_forbidden
            ]
          },
          {
            label: this.$t("finOperShort"),
            backgroundColor: "pink",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.fin_oper_allowed,
              report9.fin_oper_suspended,
              report9.fin_oper_forbidden
            ]
          }
        ];

        return {
          labels: report9labels,
          datasets
        };
      }

      return {
        labels: report9labels,
        datasets: []
      };
    },
    report4datasets() {
      const report4labels = [
        `${this.$t("eventsAmountShort")} ${this.$t("fl")}`,
        `${this.$t("eventsAmountShort")} ${this.$t("ep")}`,
        `${this.$t("eventsAmountShort")} ${this.$t("ul")}`,
        `${this.$t("caseCountShort")} ${this.$t("fl")}`,
        `${this.$t("caseCountShort")} ${this.$t("ep")}`,
        `${this.$t("caseCountShort")} ${this.$t("ul")}`
      ];
      if (this.report2 && this.report2.cases) {
        const cases = this.report2.cases;
        const events = this.report2.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          datasets.push({
            label: cases[i].country_name,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events[i].fl,
              events[i].ip,
              events[i].ul,
              cases[i].fl,
              cases[i].ip,
              cases[i].ul
            ]
          });
        }

        return {
          labels: report4labels,
          datasets
        };
      }

      return {
        labels: report4labels,
        datasets: []
      };
    }
  }),
  methods: {
    ...mapActions({
      getReport9: "admin/getReport9",
      getReport2: "admin/getReport2",
      getReport3: "admin/getReport3",
      getReport1: "admin/getReport1"
    })
  }
};
</script>

<style>
.onerow {
  max-height: calc(100%-100px);
}

.cardcontainer {
  border-radius: 16px;
  background: white;
  box-shadow: 0 5px 10px rgba(154, 160, 185, 0.05),
    0 15px 40px rgba(166, 173, 201, 0.2);
}
</style>
