<template>
  <CustomTable
    :data="djList"
    :columns="columns"
    :tabletitle="$t('djListSettings')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isAddAvailable="true"
    :loading="loading"
    :filter.sync="filter"

  >

<!-- 
    <template v-slot:event_type_id="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
              eventTypeList.length
                ? eventTypeList.find(x => x.id == props.row.event_type_id).event_type
                : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>  -->

     <!-- <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template> -->
    
    <template v-slot:p_comment="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row.p_comment.slice(0,10)
            }} ...
            <q-tooltip> {{props.row.p_comment}} </q-tooltip>
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

     <template v-slot:p_active_status="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row.p_active_status ? 'Да' : 'Нет'
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

        <template v-slot:p_deceased="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row.p_deceased ? 'Мертв' : 'Жив'
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

     <template v-slot:p_gender="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row.p_gender ? 'М' : 'Ж'
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:p_occ_date="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row && props.row.p_occ_date ? formatDateWithOnlyYear(props.row.p_occ_date) : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

     <template v-slot:p_birth_date="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row && props.row.p_birth_date ? formatDateWithOnlyYear(props.row.p_birth_date) : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

     <template v-slot:changedate="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            {{
                props.row && props.row.changedate ? formatDateWithOnlyYear(props.row.changedate) : ""
            }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";
import datemixin from "../mixins/datemixin"

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      djList: state => state.admin.djList,
    }),

    columns() {
      return  [
        {
          name: "ID",
          label: "ID",
          field: row => row.ID,
          sortable: true,
          align: "center"
        },
        {
          name: "p_name",
          label: this.$t('fio'),
          field: row => row.p_name,
          align: "center"
        },
        {
          name: "p_dict",
          label: this.$t('dictionary'),
          field: row => row.p_dict,
          align: "center"
        },
        {
          name: "p_comment",
          label: this.$t('comment'),
          field: row => row.p_comment,
          sortable: true,
          align: "center"
        },
        {
          name: "p_birth_date",
          label: this.$t('birthdate'),
          field: row => row.p_birth_date,
          align: "center"
        },
        {
          name: "p_occupation",
          label: this.$t('occupationType'),
          field: row => row.p_occupation,
          align: "center"
        },
        {
          name: "p_occ_date",
          label: this.$t('occupationStartDate'),
          field: row => row.p_occ_date,
          align: "center"
        },
         {
          name: "p_external_id",
          label: this.$t('djId'),
          field: row => row.p_external_id,
          align: "center"
        },
         {
          name: "p_gender",
          label: this.$t('gender'),
          field: row => row.p_gender,
          align: "center"
        },
         {
          name: "p_deceased",
          label: this.$t('aliveOrDeceased'),
          field: row => row.p_deceased,
          align: "center"
        },
         {
          name: "p_active_status",
          label: this.$t('active'),
          field: row => row.p_active_status,
          align: "center"
        },
         {
          name: "changedate",
          label: this.$t('updateDate'),
          field: row =>  row.changedate,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
        getDjList: "admin/getDjList"
    }),

 
    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getDjList({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("ID,p_name"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },
  }
};
</script>
