<template>
  <CustomTable
    :data="terroristOfacIndv"
    :columns="columns"
    :tabletitle="$t('terroristOfacIndv')"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :isEditAvailable="false"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    :loading="loading"
    @onRefresh="onRequest({ filter, pagination })"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions, mapMutations } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      loading: false,
      pagination: {
        sortBy: "fname",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      filter: ""
    };
  },

  computed: {
    ...mapState({
      terroristOfacIndv: state => state.admin.terroristOfacIndv
    }),
    columns() {
      return [{
          name: "fname",
          label: this.$t('firstName'),
          field: row => row.fname,
          sortable: true,
          align: "center"
        },
        {
          name: "lname",
          label: this.$t('lastname'),
          field: row => row.lname,
          align: "center",
                    sortable: true,
        },
        {
          name: "num",
          label: this.$t('number'),
          field: row => row.num,
          align: "center",
                    sortable: true,
        },
        {
          name: "title",
          label: this.$t('title'),
          field: row => row.title,
          align: "center",
                    sortable: true,
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }]
    }
  },

  methods: {
    ...mapActions({
      getTerroristsOfacIndv: "admin/getTerroristsOfacIndv"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getTerroristsOfacIndv({
               page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "fname,lname,num,title"
        ),
        filter_values: filter + "," + filter + ',' + filter + ',' + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
