<template>
  <CustomTable
    :data="employeeBlackList"
    :columns="columns"
    :tabletitle="$t('ipadressblacklist')"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :isEditAvailable="false"
     :pagination.sync="pagination"
    @onRequest="onRequest"
        :loading="loading"
                  @onRefresh="onRequest({filter, pagination})"
    :filter.sync="filter"
  >
  <!-- test -->
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
     
       pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      employeeBlackList: state => state.admin.employeeBlackList
    }),
    columns() {
      return [
        {
           name: "client_id",
          label: this.$t('iin'),
          field: row => row.client_id,
          sortable: true,
          align: "center"
        },
        {
          name: "client_name",
          label: "ФИО",
          field: row => row.client_name,
          sortable: true,
          align: "center"
        },
          {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getEmployeesBlackList: "admin/getEmployeesBlackList",
    }),

     async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber  = await this.getEmployeesBlackList({
          page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "client_id"
        ),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false
    },

  

  }
};
</script>
