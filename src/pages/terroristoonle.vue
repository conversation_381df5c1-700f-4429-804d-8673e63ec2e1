<template>
  <CustomTable
    :data="terroristsOonLe"
    :columns="columns"
    :tabletitle="$t('terroristOonLe')"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :isEditAvailable="false"
    :pagination.sync="pagination"
    :loading="loading"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      pagination: {
        sortBy: "first_name",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      terroristsOonLe: state => state.admin.terroristsOonLe
    }),
    columns() {
      return [
        {
          name: "first_name",
          label: this.$t('firstName'),
          field: row => row.first_name,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.note,
          align: "center"
        },
        {
          name: "num",
          label: this.$t('number'),
          field: row => row.num,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getTerroristsOonLe: "admin/getTerroristsOonLe"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getTerroristsOonLe({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("first_name,num"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
