<template>
  <CustomTable
    :data="terroristEsIndv"
    :columns="columns"
    :tabletitle="$t('terroristEsIndv')"
    :isDeleteAvailable="false"
    :isAddAvailable="false"
    :isEditAvailable="false"
    :pagination.sync="pagination"
    :loading="loading"
    @onRequest="onRequest"
              @onRefresh="onRequest({filter, pagination})"
                  :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      pagination: {
        sortBy: "wname",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      terroristEsIndv: state => state.admin.terroristEsIndv
    }),
    columns() {
      return [
        {
          name: "wname",
          label: this.$t('fullName'),
          field: row => row.wname,
          align: "center",
          sortable: true
        },
        {
          name: "wname_en",
          label: this.$t('fullNameEng'),
          field: row => row.wname_en,
          align: "center",
                    sortable: true

        },
        {
          name: "fname",
          label: this.$t('firstName'),
          field: row => row.fname,
          align: "center",
                    sortable: true

        },
        {
          name: "fname_en",
          label: this.$t('firstNameEng'),
          field: row => row.fname_en,
          align: "center",
                    sortable: true

        },
        {
          name: "lname",
          label: this.$t('lastname'),
          field: row => row.lname,
          align: "center",
                    sortable: true

        },
        {
          name: "lname_en",
          label: this.$t('lastnameEng'),
          field: row => row.lname_en,
          align: "center",
                    sortable: true

        },
        {
          name: "mname",
          label: this.$t('middleName'),
          field: row => row.mname,
          align: "center",
                    sortable: true

        },
        {
          name: "mname_en",
          label: this.$t('middleNameEng'),
          field: row => row.mname_en,
          align: "center",
                    sortable: true

        },
        {
          name: "note",
          label: this.$t('notes'),
          field: row => row.note,
          align: "center"
        },

        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  },

  methods: {
    ...mapActions({
      getTerroristsEsIndv: "admin/getTerroristsEsIndv"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber  = await this.getTerroristsEsIndv({
         page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent(
          "wname,wname_en,fname,fname_en,lname,lname_en,mname,mname_en"
        ),
        filter_values: filter + "," + filter + "," + filter + "," + filter + "," + filter + "," + filter + "," + filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false
    }
  }
};
</script>
