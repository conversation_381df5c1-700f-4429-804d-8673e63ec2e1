<template>
  <CustomTable
    :data="integrationList"
    :columns="columns"
    @reset="reset"
    @onEditClick="onEditClick"
    @saveEdit="editIntegrationListInner"
    :tabletitle="$t('integrationChannels')"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :isDeleteAvailable="false"
    :is_edit_disabled="!integration.open_status"
    :isAddAvailable="false"
    :loading="loading"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>

    <template v-slot:is_enable="{ props }">
      <q-item>
        <q-item-section>
          <!-- <q-icon name="done" v-if="props.row.is_enable"/> -->
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:send_result="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:create_case="{ props }">
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-icon
              name="done"
              color="green"
              size="sm"
              v-if="props.row.is_enable"
            />
            <q-icon name="cancel" color="red" size="sm" v-else />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeValue')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('value')}} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  :disable="true"
                  v-model="integration.name_ru"
                  type="text"
                  outlined
                  :label="$t('value')"
                />
              </q-item-section>
            </q-item>

            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{$t('status')}} </q-item-label>
                <q-select
                  outlined
                  class="q-mt-xs"
                  :label="$t('status')"
                  dense
                  v-model="integration.open_status"
                  :options="statusOptions"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      integration: {
        name_ru: "",
        name_en: "",
        name_kz: "",
        open_status: ""
      },
      statusOptions: ["fail_open", "fail_close"],
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      filter: ""
    };
  },

  computed: {
    ...mapState({
      integrationList: state => state.admin.integrationList,
    }),

      columns() { 
        return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "name_ru",
          label: this.$t('columnName'),
          field: row => row.name_ru,
          align: "center"
        },
        {
          name: "open_status",
          label: this.$t('status'),
          field: row => row.open_status,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]},
    
  },

  methods: {
    ...mapActions({
      getIntegrationList: "admin/getIntegrationList",
      editIntegrationList: "admin/editIntegrationList"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getIntegrationList({
        page: page,
        per_page: rowsPerPage,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("name_ru"),
        filter_values: filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    onEditClick(row) {
      this.integration = {
        ...row
      };
    },

    reset() {
      this.integration = {
        name_ru: "",
        name_en: "",
        name_kz: "",
        open_status: ""
      };
    },

    editIntegrationListInner() {
      const form = {
        system_id: this.integration.id,
        open_status: this.integration.open_status
      };
      this.editIntegrationList(form)
        .then(response => {
          this.$q.notify({
            message: `Канал ${form.system_id} успешно обновлен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
  }
};
</script>
