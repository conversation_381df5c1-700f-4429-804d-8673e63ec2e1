<template>
  <CustomTable
    :data="clientswhitelist"
    :columns="columns"
    @reset="reset"
    :tabletitle="$t('clientswhitelist')"
    :is_add_disabled="
      whitelistclient.iin.length < 12 || !/^\d+$/.test(whitelistclient.iin)
    "
    :is_edit_disabled="
      whitelistclient.iin.length < 12 || !/^\d+$/.test(whitelistclient.iin)
    "
    :isDeleteAvailable="true"
    :isAddAvailable="true"
    :isEditAvailable="true"
    :loading="loading"
    @saveEdit="saveEdit"
    @onEditClick="onEditClick"
    @addRow="addClientToWhiteListInner"
    @deleteRow="deleteClientWhiteListInner"
    :pagination.sync="pagination"
    @onRequest="onRequest"
    @onRefresh="onRequest({ filter, pagination })"
    :filter.sync="filter"
  >
    <template v-slot:searchfield="{ props }">
      <q-input
        outlined
        dense
        debounce="300"
        v-model="filter"
        :placeholder="$t('search')"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </template>
    <template v-slot:editModal>
      <q-card-section>
        <div class="text-h6">
          {{$t('changeClient')}}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{ $t("iinbin") }}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  maxlength="12"
                  type="text"
                  v-model="whitelistclient.iin"
                  outlined
                  :label="$t('iinbin')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{ $t("notes") }} </q-item-label>
                <q-input
                  class="q-mt-xs"
                  autogrow
                  dense
                  v-model="whitelistclient.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>

    <template v-slot:addModal>
      <q-card-section>
        <div class="text-h6">
          {{ $t("addClient") }}
          <q-btn
            round
            flat
            dense
            icon="close"
            class="float-right"
            color="grey-8"
            v-close-popup
          ></q-btn>
        </div>
      </q-card-section>
      <q-separator inset></q-separator>
      <q-card-section class="q-pt-none">
        <q-form class="q-gutter-md">
          <q-list>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs">{{ $t("iinbin") }}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  maxlength="12"
                  type="text"
                  v-model="whitelistclient.iin"
                  outlined
                  :label="$t('iinbin')"
                />
              </q-item-section>
            </q-item>
            <q-item class="q-px-none">
              <q-item-section>
                <q-item-label class="q-pb-xs"> {{ $t("notes") }}</q-item-label>
                <q-input
                  class="q-mt-xs"
                  dense
                  v-model="whitelistclient.note"
                  outlined
                  :label="$t('notes')"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-form>
      </q-card-section>
    </template>
  </CustomTable>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  components: {
    CustomTable: () => import("../components/CustomTable")
  },

  data() {
    return {
      is_add_disabled: true,
      loading: false,
      whitelistclient: {
        iin: "",
        note: ""
      },
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      filter: ""
    };
  },

  computed: {
    ...mapState({
      clientswhitelist: state => state.admin.clientswhitelist
    }),

    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "iin",
          label: this.$t("iinbin"),
          field: row => row.iin,
          sortable: true,
          align: "center"
        },
        {
          name: "note",
          label: this.$t("notes"),
          field: row => row.name,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    }
  },

  methods: {
    ...mapActions({
      getClientsWhiteList: "admin/getClientsWhiteList",
      addClientWhiteList: "admin/addClientWhiteList",
      editClientWhiteList: "admin/editClientWhiteList",
      deleteClientWhiteList: "admin/deleteClientWhiteList"
    }),

    async onRequest(props) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getClientsWhiteList({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: encodeURIComponent("id,iin"),
        filter_values: filter + "," + filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },

    reset() {
      this.whitelistclient = {
        iin: "",
        note: ""
      };
    },

    onEditClick(row) {
      console.log(row);
      this.whitelistclient = { ...row };
    },

    saveEdit() {
      this.editClientWhiteList(this.whitelistclient)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${response.id} изменен`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    deleteClientWhiteListInner(row) {
      this.deleteClientWhiteList(row.id)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${row.id} удален из белого списка`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    },

    addClientToWhiteListInner() {
      this.addClientWhiteList(this.whitelistclient)
        .then(response => {
          this.$q.notify({
            message: `Клиент ${this.whitelistclient.iin} добавлен в белый список`,
            color: "green"
          });
        })
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }
  }
};
</script>
