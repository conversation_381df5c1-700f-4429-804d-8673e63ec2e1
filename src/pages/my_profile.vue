<template>
  <q-page>
    <div class="row q-col-gutter-sm q-ma-xs ">
      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <q-card class="my-card" flat bordered>
          <q-card-section horizontal>
            <q-card-section class="q-pt-xs col">
              <div class="text-h5 q-mt-sm q-mb-xs">
                {{
                  user.first_name && user.last_name
                    ? user.first_name + " " + user.last_name
                    : ""
                }}
              </div>
            </q-card-section>

            <q-card-section class="col-5 flex flex-center">
              <q-img
                class="rounded-borders"
                src="https://cdn.quasar.dev/img/boy-avatar.png"
              />
            </q-card-section>
          </q-card-section>

          <q-separator />

          <!-- <q-card-section>
            Assessing clients needs and present suitable promoted products. Liaising with and persuading targeted doctors to prescribe our products utilizing effective sales skills.
          </q-card-section> -->
        </q-card>
      </div>
      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <q-card>
          <q-card-section>
            <q-form class="q-gutter-md">
              <q-input
                filled
                v-model="user.first_name"
                :label="$t('name')"
                :disable="true"
              />

              <q-input
                filled
                v-model="user.last_name"
                :label="$t('lastname')"
                :disable="true"
              />

              <q-input
                filled
                v-model="user.email"
                :label="$t('login')"
                :disable="true"
                f
              />

              <q-input
                :type="isPwd1 ? 'password' : 'text'"
                filled
                v-model="newPass"
                :label="$t('newPassword')"
                lazy-rules
                ref="tempPassword"
                :rules="[
                  val =>
                    (val && val.match(/[a-z]/g)) || this.$t('wordsInLowRegister'),
                  val =>
                    (val && val.match(/[A-Z]/g)) || this.$t('wordsInHigherRegister'),
                  val => (val && val.match(/[0-9]/g)) || this.$t('numbers'),
                  val => (val && val.length > 7) || this.$t('min8Symbols')
                ]"
              >
                <template v-slot:append>
                  <q-icon
                    :name="isPwd1 ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="isPwd1 = !isPwd1"
                  />
                </template>
              </q-input>

              <q-input
                :type="isPwd2 ? 'password' : 'text'"
                filled
                v-model="tempPass"
                lazy-rules
                :label="$t('oldPassword')"
                bottom-slots
                :error="resetPasswordError"
                :rules="[
                  val => !!val || $t('requiredField'),
                  val =>
                    val.length > 8 ||
                    $t('passwordMustContainAtLeast8Symbold')
                ]"
              >
                <template v-slot:error>
                  {{ resetPasswordErrorText }}
                </template>
                <template v-slot:append>
                  <q-icon
                    :name="isPwd2 ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="isPwd2 = !isPwd2"
                  />
                </template>
              </q-input>

              <q-select
                filled
                :label="$t('role')"
                :disable="true"
                v-model="user.role_id"
                :options="roleOptions"
                option-label="label"
                option-value="id"
                emit-value
                map-options
              />

              <div>
                <q-btn
                  :label="$t('update')"
                  type="submit"
                  @click="sendNewPassword"
                  color="primary"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";
export default {
  data() {
    return {
      tempPass: "",
      newPass: "",
      isPwd1: true,
      isPwd2: true,
      resetPasswordError: false,
      resetPasswordErrorText: "",
      roleOptions: [
        {
          id: 1,
          label: "Администратор"
        },
        {
          id: 2,
          label: "Фрод-Аналитик"
        },
        {
          id: 3,
          label: "Руководитель"
        }
      ]
    };
  },
  computed: mapState({
    user: state => state.auth.currentUser
  }),
  methods: {
    ...mapActions({
      changePassword: "auth/changePassword",
      logout: "auth/logout"
    }),
    sendNewPassword() {
      this.$refs.tempPassword.validate();

      if (!this.$refs.tempPassword.hasError) {
        this.changePassword({
          new_password: this.newPass,
          old_password: this.tempPass
        })
          .then(response => {
            this.$q.notify({
              message: response,
              color: "green"
            });
            this.resetPasswordError = false;
            this.resetPasswordErrorText = "";
            this.logout()
              .then((response) => {
                this.$router.push({
                  name: "login"
                })
              })
              .catch(err => {
                this.$q.notify({
                  message: "Произлошла Ошибка"
                });
              });
          })
          .catch(err => {
            this.resetPasswordError = true;
            this.resetPasswordErrorText = err.message;
          });
      }
    }
  }
};
</script>

<style scoped></style>
