<script>
import { Bar, mixins } from "vue-chartjs";
const { reactiveProp } = mixins


export default {
  extends: Bar,
    mixins: [reactiveProp],
  props: {
    options: {
      type: Object,
      default: function() {
        return {
          scales: {
            yAxes: [
              {
                ticks: {
                  beginAtZero: true
                },
                gridLines: {
                  display: true
                }
              }
            ],
            xAxes: [
              {
                ticks: {
                  beginAtZero: true
                },
                gridLines: {
                  display: true
                }
              }
            ]
          },
          responsive: true,
          maintainAspectRatio: false,

        };
      }
    }
  },
  mounted() {
    this.renderChart(this.chartData, this.options);
  }
};
</script>
