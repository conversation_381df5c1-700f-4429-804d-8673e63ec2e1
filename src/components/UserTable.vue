<template>
  <q-page class="q-pa-lg">
    <q-card class="q-pa-none">
      <q-dialog v-model="edit_user" @hide="resetUser">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              {{$t('changeUser')}}
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-separator inset></q-separator>
          <q-card-section class="q-pt-none">
            <q-form class="q-gutter-md">
              <q-list>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs">{{$t('name')}}</q-item-label>
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="newuser.first_name"
                      :label="$t('name')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"> {{$t('lastname')}}</q-item-label>
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="newuser.last_name"
                      :label="$t('lastname')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"> {{$t('email')}} </q-item-label>
                    <q-input
                      :rules="[
                        val => !!val || 'Email is missing',
                        isValidEmail
                      ]"
                      class="q-mt-xs"
                      v-model="newuser.email"
                      dense
                      outlined
                      type="email"
                      :label="$t('email')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs">{{$t('role')}}</q-item-label>
                    <q-select
                      outlined
                      class="q-mt-xs"
                      :label="$t('role')"
                      dense
                      v-model="newuser.role_id"
                      :options="roles"
                      :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                      option-value="id"
                      emit-value
                      map-options
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('change')"
              type="submit"
              color="primary"
              @click="editUser"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>
      <q-dialog v-model="new_user" @hide="resetUser">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              {{$t('addNewUser')}}
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-separator inset></q-separator>
          <q-card-section class="q-pt-none">
            <q-form class="q-gutter-md">
              <q-list>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs">{{$t('name')}}</q-item-label>
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="newuser.first_name"
                      :label="$t('name')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"> {{$t('lastname')}}</q-item-label>
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="newuser.last_name"
                      :label="$t('lastname')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"> {{$t('email')}} </q-item-label>
                    <q-input
                      :rules="[
                        val => !!val || 'Email is missing',
                        isValidEmail
                      ]"
                      class="q-mt-xs"
                      v-model="newuser.email"
                      dense
                      outlined
                      type="email"
                      :label="$t('email')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs">{{$t('role')}}</q-item-label>
                    <q-select
                      outlined
                      class="q-mt-xs"
                      :label="$t('role')"
                      dense
                      v-model="newuser.role_id"
                      :options="roles"
                      :option-label="currLang == 'ru' ? 'name_ru' : 'name_kz'"
                      option-value="id"
                      emit-value
                      map-options
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('add')"
              :disable="
                !(
                  newuser.first_name &&
                  newuser.last_name &&
                  newuser.role_id &&
                  newuser.email
                )
              "
              @click="$emit('addUser', newuser)"
              type="submit"
              color="primary"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-card-section>
        <div class="text-h6 text-grey-8">
          {{$t('users')}}
          <q-btn
            :label="$t('addUser')"
            @click="new_user = true"
            class="float-right text-capitalize text-indigo-8 shadow-3"
            icon="person"
          />
        </div>
      </q-card-section>
      <q-card-section class="q-pa-none q-mt-md">
        <q-table :data="users" :columns="columns"  row-key="name">
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="first_name" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.first_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="last_name" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.last_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="email" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.email }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="role" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{
                      roles.length && findRole(props.row.role_id)
                    }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="actions" :props="props">
                <q-btn
                  icon="edit"
                  @click="openEditModal(props.row)"
                  size="sm"
                  flat
                  dense
                />
                <q-btn icon="delete" size="sm" class="q-ml-sm" flat dense @click="$emit('deleteUser', props.row)"/>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script>

export default {
  name: "TableActions",
  props: {
    users: {
      type: Array,
      default: []
    },
    roles: {
      type: Array,
      default: []
    },
    changeUserRoleInner: {
      type: Function,
      default: () => {}
    },
    addUser: {
      type: Function,
      default: () => {}
    },
    deleteUser: {
      type: Function,
      default: () => {}
    },
    editUserInner: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      newuser: {
        first_name: "",
        last_name: "",
        email: "",
        role_id: ""
      },
      new_user: false,
      edit_user: false,
      isPwd: true,

    };
  },

  computed: {
    columns() {
      return [
        {
          name: "first_name",
          label: this.$t('name'),
          field: row => row.first_name,
          sortable: true,
          align: "center"
        },
        {
          name: "last_name",
          label: this.$t('lastname'),
          field: row => row.last_name,
          align: "center"
        },
        {
          name: "email",
          label: this.$t('email'),
          field: row => row.email,
          align: "center"
        },
        {
          name: "role",
          label: this.$t('role'),
          field: row => row.role,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false,
        }
      ]
    }
  },

  methods: {
    findRole(id) {
      const role = this.roles.find(role => role.id == id)
      return this.currLang == 'ru' ? role.name_ru : role.name_kz
    },
    editUser() {
      this.$emit('editUserInner', this.newuser)
      this.resetUser()
    },

    isValidEmail(val) {
      const emailPattern = /^(?=[a-zA-Z0-9@._%+-]{6,254}$)[a-zA-Z0-9._%+-]{1,64}@(?:[a-zA-Z0-9-]{1,63}\.){1,8}[a-zA-Z]{2,63}$/;
      return emailPattern.test(val) || "Invalid email";
    },

    openEditModal(row) {
      this.newuser = {
        id: row.id,
        first_name: row.first_name,
        last_name: row.last_name,
        email: row.email,
        role_id: row.role_id
      };
      this.edit_user = true;
    },

    resetUser() {
      this.newuser = {
        first_name: "",
        last_name: "",
        email: "",
        role_id: ""
      };
    }
  }
};
</script>

<style scoped></style>
