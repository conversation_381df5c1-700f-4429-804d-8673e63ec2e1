<template>
  <q-page class="q-pa-lg">
    <q-card class="q-pa-none">
      <q-dialog v-model="add_role" @hide="resetRole">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              Добавить новую роль
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-separator inset></q-separator>
          <q-card-section class="q-pt-none">
            <q-form class="q-gutter-md">
              <q-list>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      >{{$t('roleNameRu')}}</q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.name_ru"
                      :label="$t('roleNameRu')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      >{{$t('roleNameEng')}}</q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.main_name"
                      :label="$t('roleNameEng')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      >{{$t('roleNameKz')}}</q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.name_kz"
                      :label="$t('roleNameKz')"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('add')"
              :disable="!(role.main_name && role.name_ru && role.name_kz)"
              @click="$emit('addNewRole', {...role, name_en: role.main_name})"
              type="submit"
              color="primary"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

       <q-dialog v-model="edit_role" @hide="resetRole">
        <q-card style="width: 600px; max-width: 60vw;">
          <q-card-section>
            <div class="text-h6">
              {{$t('changeRole')}}
              <q-btn
                round
                flat
                dense
                icon="close"
                class="float-right"
                color="grey-8"
                v-close-popup
              ></q-btn>
            </div>
          </q-card-section>
          <q-separator inset></q-separator>
          <q-card-section class="q-pt-none">
            <q-form class="q-gutter-md">
              <q-list>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      >{{$t('roleNameRu')}}</q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.name_ru"
                      :label="$t('roleNameRu')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      > {{$t('roleNameEng')}} </q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.main_name"
                      :label="$t('roleNameEng')"
                    />
                  </q-item-section>
                </q-item>
                <q-item class="q-px-none">
                  <q-item-section>
                    <q-item-label class="q-pb-xs"
                      >{{$t('roleNameKz')}}</q-item-label
                    >
                    <q-input
                      class="q-mt-xs"
                      dense
                      outlined
                      v-model="role.name_kz"
                      :label="$t('roleNameKz')"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-form>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
            <q-btn
              :label="$t('change')"
              :disable="!(role.main_name && role.name_ru && role.name_kz)"
              @click="$emit('editRoleInner', {...role, name_en: role.main_name})"
              type="submit"
              color="primary"
              v-close-popup
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-card-section>
        <div class="text-h6 text-grey-8">
          {{$t('roles')}}
          <q-btn
            :label="$t('addRole')"
            @click="add_role = true"
            class="float-right text-capitalize text-indigo-8 shadow-3"
            icon="person"
          />
        </div>
      </q-card-section>
      <q-card-section class="q-pa-none q-mt-md">
        <q-table :data="roles" :columns="columns" row-key="id">
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="id" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.id }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="name_ru" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.name_ru }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
               <q-td key="name_kz" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.name_kz }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="main_name" :props="props">
                <q-item >
                  <q-item-section>
                    <q-item-label>{{ props.row.main_name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-td>
              <q-td key="actions" :props="props">
                <q-btn
                  icon="edit"
                  size="sm"
                  @click="onEditClick(props.row)"
                  flat
                  dense
                />
                <q-btn icon="delete" size="sm" class="q-ml-sm" flat dense @click="$emit('deleteRole',props.row)" />
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  name: "TableActions",
  props: {
    roles: {
      type: Array,
      default: []
    },
    addNewRole: {
      type: Function,
      default: () => {}
    },
    deleteRole: {
      type: Function,
      default: () => {}
    },
    editRoleInner: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      edit_role: false,
      role: {
        main_name: "",
        name_ru: "",
        name_en: "",
        name_kz: ""
      },
      add_role: false
    };
  },

  computed: {
    columns() {
      return [
        {
          name: "id",
          label: "ID",
          field: row => row.id,
          sortable: true,
          align: "center"
        },
        {
          name: "name_ru",
          label: this.$t('roleNameRu'),
          field: row => row.name_ru,
          sortable: true,
          align: "center"
        },
         {
          name: "name_kz",
          label: this.$t('roleNameKz'),
          field: row => row.name_kz,
          sortable: true,
          align: "center"
        },
        {
          name: "main_name",
          label: this.$t('roleNameEng'),
          field: row => row.main_name,
          sortable: true,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false,
        }
      ]
    }
  },

  methods: {
    onEditClick(row) {
      this.role = {...row}
      this.edit_role = true
    },

    resetRole() {
      this.role = {
        main_name: "",
        name_ru: "",
        name_en: "",
        name_kz: ""
      };
    }
  }
};
</script>

<style scoped></style>
