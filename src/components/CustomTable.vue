<template>
  <q-card  >
    <q-dialog v-model="edit_item" @hide="$emit('reset')">
      <q-card style="width: 600px; max-width: 60vw;">
        <slot name="editModal"> </slot>
        <q-card-actions align="right" class="text-teal">
          <q-btn
            :label="$t('change')"
            type="submit"
            @click="$emit('saveEdit')"
            color="primary"
            v-close-popup
            :disable="is_edit_disabled"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="new_item" @hide="$emit('reset')">
      <q-card style="width: 600px; max-width: 60vw;">
        <slot name="addModal"> </slot>
        <q-card-actions align="right" class="text-teal">
          <q-btn
            :label="$t('add')"
            @click="$emit('addRow')"
            type="submit"
            color="primary"
            v-close-popup
            :disable="is_add_disabled"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-card-section class="q-pa-none">
      <q-table
        :data="data"
        :columns="columns"
        row-key="name"
        :filter="filter"
        :title="tabletitle"
        :no-data-label="$t('recordNotFound')"
        :no-results-label="$t('recordNotFound')"
        :pagination.sync="paginationInner"
        :grid="mode == 'grid'"
        :loading="loading"
        @request="onRequestInner"
              binary-state-sort

      >
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td
              v-for="column in columns.slice(0, columns.length - 1)"
              :key="column.name"
              :props="props"
              :style="{ width: '400px', whiteSpace: 'normal' }"
            >
              <slot :name="column.name" :props="props">
                <q-item>
                  <q-item-section>
                    <q-item-label>{{ props.row[column.name] }}</q-item-label>
                  </q-item-section>
                </q-item>
              </slot>
            </q-td>

            <q-td key="actions" :props="props">
              <q-btn
                icon="edit"
                v-if="isEditAvailable"
                @click="openEditModal(props.row)"
                size="sm"
                flat
                dense
              />
              <q-btn
                v-if="isDeleteAvailable"
                icon="delete"
                size="sm"
                class="q-ml-sm"
                flat
                dense
                @click="$emit('deleteRow', props.row)"
              />
            </q-td>
          </q-tr>
        </template>

        <template v-slot:top-right="props">
          <q-btn
            :label="$t('add')"
            v-if="isAddAvailable"
            @click="new_item = true"
            class="float-right text-capitalize text-indigo-8 shadow-3 q-mr-lg"
          />

          <slot name="searchfield" :props="props">
          </slot>

          <q-icon name="refresh" class="q-ml-sm" size="sm" @click="$emit('onRefresh')">
            <q-tooltip :disable="$q.platform.is.mobile" v-close-popup>
              Обновить
            </q-tooltip>
          </q-icon>

          <q-btn
            flat
            round
            dense
            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
            @click="props.toggleFullscreen"
            v-if="mode === 'list'"
          >
            <q-tooltip :disable="$q.platform.is.mobile" v-close-popup
              >{{
                props.inFullscreen ? "Exit Fullscreen" : "Toggle Fullscreen"
              }}
            </q-tooltip>
          </q-btn>

          <q-btn
            flat
            round
            dense
            :icon="mode === 'grid' ? 'list' : 'grid_on'"
            @click="
              mode = mode === 'grid' ? 'list' : 'grid';
              separator = mode === 'grid' ? 'none' : 'horizontal';
            "
            v-if="!props.inFullscreen"
          >
            <q-tooltip :disable="$q.platform.is.mobile" v-close-popup
              >{{ mode === "grid" ? "List" : "Grid" }}
            </q-tooltip>
          </q-btn>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  name: "TableActions",
  props: {
    filter: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: []
    },
    pagination: {
      type: Object,
      default: () => {}
    },
    columns: {
      type: Array,
      default: []
    },
    deleteRow: {
      type: Function,
      default: () => {}
    },
    onEditClick: {
      type: Function,
      default: () => {}
    },
    reset: {
      type: Function,
      default: () => {}
    },
    saveEdit: {
      type: Function,
      default: () => {}
    },
    addRow: {
      type: Function,
      default: () => {}
    },
    tabletitle: {
      type: String,
      default: ""
    },
    isDeleteAvailable: {
      type: Boolean,
      default: true
    },
    isAddAvailable: {
      type: Boolean,
      default: true
    },
    isEditAvailable: {
      type: Boolean,
      default: true
    },
    is_add_disabled: {
      type: Boolean,
      default: false
    },
    is_edit_disabled: {
      type: Boolean,
      default: false
    },
    onRequest: {
      type: Function,
      default: () => {}
    },
    loading: {
      type: Boolean,
      default: false
    },
    onRefresh: {
      type: Function,
      default: () => {}
    }
  },

  mounted() {
    this.$emit("onRequest", {
      pagination: this.pagination,
      filter: ""
    });
  },
  computed: {
    paginationInner: {
      get() {
        return this.pagination;
      },
      set(newVal) {
        this.$emit("update:pagination", newVal);
      }
    }
  },
  data() {
    return {
      new_item: false,
      edit_item: false,
      mode: "list"
    };
  },

  methods: {
    onRequestInner(props) {
      this.$emit("onRequest", props);
    },
    openEditModal(row) {
      this.edit_item = true;
      this.$emit("onEditClick", row);
    }
  }
};
</script>

<style scoped></style>
