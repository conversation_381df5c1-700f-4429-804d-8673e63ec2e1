
import { mapState, mapActions } from "vuex";


export default {

     computed: {
        ...mapState({
          user: state => state.auth.currentUser,
          currLang() {
            return  this.$i18n.locale
          }
        }),
    } ,

    methods: {

      ...mapActions({
        getAllUsers: "admin/getAllUsers",
      }),

      convertTimeMeasurementToLang(measurement) {
        let [val, measure] = measurement.split(" ")
        val = +val;
        if (measure == 'year' || measure == 'years') {
          if (val == 1) {
            return val + " " + this.$t('year')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('years')
          } else {
            return val + " " + this.$t('years2')
          }
        } else if (measure == 'month' || measure == 'months') {
          if (val == 1) {
            return val + " " + this.$t('month')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('months')
          } else {
            return val + " " + this.$t('months2')
          }
        } else if (measure == 'hour' || measure == 'hours') {
          if (val == 1) {
            return val + " " + this.$t('hour')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('hours')
          } else {
            return val + " " + this.$t('hours2')
          }
        } else if (measure == 'minute' || measure == 'minutes') {
          if (val == 1) {
            return val + " " + this.$t('minute')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('minutes')
          } else {
            return val + " " + this.$t('minutes2')
          }
        }  else if (measure == 'day' || measure == 'days') {
          if (val == 1) {
            return val + " " + this.$t('day')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('days')
          } else {
            return val + " " + this.$t('days2')
          }
        } else if (measure == 'second' || measure == 'seconds') {
          if (val == 1) {
            return val + " " + this.$t('second')
          } else if (val == 2 || val ==3 || val == 4) {
            return val + " " + this.$t('seconds')
          } else {
            return val + " " + this.$t('seconds2')
          }
        }
        return ""
      },

          printUserName (id) {
              if (!this.users.length) return "";
              const user = this.users.find(user => user.id == id);
              return user ? user.first_name : "";
          },

          printLastName (id) {
              if (!this.users.length) return "";
              const user = this.users.find(user => user.id == id);
              return user ? user.last_name : "";
          },

          printEmail (id) {
              if (!this.users.length) return "";
              const user = this.users.find(user => user.id == id);
              return user ? user.email : "";
          },

          printUserRole (id) {
              if (!this.users.length) {
                return ""
              }
              const user = this.users.find(user => user.id == id);
              return user && user.role ? (this.currLang == 'ru' ? user.role.name_ru : user.role.name_kz) : "";
          },

          printFullName(id) {
            const user = this.users.length ? this.users.find(user => user.id == id) : ""
            return user ? (user.first_name + " "  + user.last_name) : ""
          },

          printFullNameEmail (id) {
            const user = this.users.length ? this.users.find(user => user.id == id) : ""
            return user ? (user.first_name + " "  + user.last_name + " (" + user.email + ")") : ""
          }
    }
}
