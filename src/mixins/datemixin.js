import { date } from "quasar";

// date mixin
export default {
  methods: {
    /*
     *  params: Date obj
     *  result YYYY-MM-DDTHH:MM:SS.SSSZ
     */

    formatDateToDB: function(timeStamp) {
      let formattedString = new Date(timeStamp + " UTC").toISOString();
      return formattedString;
    },

    /*
     * params: Date obj
     * result YYYY-MM-DD HH:mm
     */
    formatDateDefault: function(timeStamp) {
      let formattedString = date.formatDate(timeStamp, "YYYY-MM-DD HH:mm");
      return formattedString;
    },

    formatWay4: function(timeStamp) {
      let formattedString = date.formatDate(timeStamp, "DD.MM.YYYY HH:mm:ss");
      return formattedString;
    },

    formatDateWithOnlyYear: function(timeStamp) {
      let formattedString = date.formatDate(timeStamp, "YYYY-MM-DD");
      return formattedString;
    },

    formatDateFact: function(timeStamp) {
      // let timeStampUpdate = this.addorsubDate(timeStamp, 0, 0, 6,0,'sub')
      let formattedString = date.formatDate(
        timeStamp,
        this.currLang == 'ru' ? "D MMM YYYY г. HH:mm" : "D MMM YYYY ж. HH:mm",
        {
          days: [
            this.$t("sunday"),
            this.$t("monday"),
            this.$t("tuesday"),
            this.$t("wednesday"),
            this.$t("thursday"),
            this.$t("friday")
          ],
          months: [
            this.$t("january"),
            this.$t("february"),
            this.$t("march"),
            this.$t("april"),
            this.$t("may"),
            this.$t("june"),
            this.$t("july"),
            this.$t("august"),
            this.$t("september"),
            this.$t("october"),
            this.$t("november"),
            this.$t("december")
          ],
          monthsShort: [
            this.$t("janShort"),
            this.$t("febShort"),
            this.$t("marchShort"),
            this.$t("aprilShort"),
            this.$t("mayShort"),
            this.$t("juneShort"),
            this.$t("julyShort"),
            this.$t("augustShort"),
            this.$t("septemberShort"),
            this.$t("octoberShort"),
            this.$t("novemberShort"),
            this.$t("decemberShort")
          ]
        }
      );
      return formattedString;
    },

    /*
     *   params (2020-08-19T10:20:40.424523Z)
     *   result: В Среду в 10:20
     */
    formatDate: function(datedb) {
      let timeStampUpdate = this.addorsubDate(datedb, 0, 0, 6, 0, "sub");
      let formattedString = date.formatDate(timeStampUpdate, "D MMM в HH:mm", {
        days: [
          this.$t("sunday"),
          this.$t("monday"),
          this.$t("tuesday"),
          this.$t("wednesday"),
          this.$t("thursday"),
          this.$t("friday"),
          this.$t('saturday')
        ],
        months: [
          this.$t("january"),
          this.$t("february"),
          this.$t("march"),
          this.$t("april"),
          this.$t("may"),
          this.$t("june"),
          this.$t("july"),
          this.$t("august"),
          this.$t("september"),
          this.$t("october"),
          this.$t("november"),
          this.$t("december")
        ],
        monthsShort: [
          this.$t("janShort"),
          this.$t("febShort"),
          this.$t("marchShort"),
          this.$t("aprilShort"),
          this.$t("mayShort"),
          this.$t("juneShort"),
          this.$t("julyShort"),
          this.$t("augustShort"),
          this.$t("septemberShort"),
          this.$t("octoberShort"),
          this.$t("novemberShort"),
          this.$t("decemberShort")
        ]
      });
      return formattedString;
    },

    formatHours: function(datedb) {
      let timeStampUpdate = this.addorsubDate(datedb, 0, 0, 6, 0, "sub");
      let formattedString = date.formatDate(timeStampUpdate, "HH:mm", {
        days: [
          this.$t("sunday"),
          this.$t("monday"),
          this.$t("tuesday"),
          this.$t("wednesday"),
          this.$t("thursday"),
          this.$t("friday")
        ],
        months: [
          this.$t("january"),
          this.$t("february"),
          this.$t("march"),
          this.$t("april"),
          this.$t("may"),
          this.$t("june"),
          this.$t("july"),
          this.$t("august"),
          this.$t("september"),
          this.$t("october"),
          this.$t("november"),
          this.$t("december")
        ],
        monthsShort: [
          this.$t("janShort"),
          this.$t("febShort"),
          this.$t("marchShort"),
          this.$t("aprilShort"),
          this.$t("mayShort"),
          this.$t("juneShort"),
          this.$t("julyShort"),
          this.$t("augustShort"),
          this.$t("septemberShort"),
          this.$t("octoberShort"),
          this.$t("novemberShort"),
          this.$t("decemberShort")
        ]
      });
      return formattedString;
    },

    /*
     *   params (2020-08-19T10:20:40.424523Z)
     *   result: 19 авг
     */

    formatDateForCardSection: function(datedb) {
      let timeStampUpdate = this.addorsubDate(datedb, 0, 0, 6, 0, "sub");
      let formattedString = date.formatDate(timeStampUpdate, "D MMM ", {
        days: [
          this.$t("sunday"),
          this.$t("monday"),
          this.$t("tuesday"),
          this.$t("wednesday"),
          this.$t("thursday"),
          this.$t("friday")
        ],
        months: [
          this.$t("january"),
          this.$t("february"),
          this.$t("march"),
          this.$t("april"),
          this.$t("may"),
          this.$t("june"),
          this.$t("july"),
          this.$t("august"),
          this.$t("september"),
          this.$t("october"),
          this.$t("november"),
          this.$t("december")
        ],
        monthsShort: [
          this.$t("janShort"),
          this.$t("febShort"),
          this.$t("marchShort"),
          this.$t("aprilShort"),
          this.$t("mayShort"),
          this.$t("juneShort"),
          this.$t("julyShort"),
          this.$t("augustShort"),
          this.$t("septemberShort"),
          this.$t("octoberShort"),
          this.$t("novemberShort"),
          this.$t("decemberShort")
        ]
      });
      return formattedString;
    },

    /*
     *     Date (2017,2,7)
     *     params(dataobj,7,1)
     *     result:  `newDate` is now 2017-3-14 00:00:00
     */

    addorsubDate: function(
      modifiedDate,
      days = 0,
      month = 0,
      hours = 0,
      milliseconds = 0,
      method
    ) {
      let newDate = modifiedDate;
      if (method == "add") {
        newDate = date.addToDate(newDate, { days, month, hours, milliseconds });
      } else {
        newDate = date.subtractFromDate(newDate, {
          days,
          month,
          hours,
          milliseconds
        });
      }
      return newDate;
    },

    /*
     *     Date (2017,10,2)
     *     params(dataobj,2010,2)
     *     result:  `newDate` is now 2010-2-2
     */

    setDateorTime: function(
      modifiedDate,
      year,
      month,
      date,
      hours,
      minutes,
      seconds,
      milliseconds
    ) {
      return date.adjustDate(modifiedDate, {
        year,
        month,
        date,
        hours,
        minutes,
        seconds,
        milliseconds
      });
    },

    /*
     * [new Date(2017, 6, 24), new Date(2017, 5, 20), new Date(2017, 6, 26)] .  `min` is 2017-5-20
     */
    getMinDate: function(datearr) {
      let min = date.getMinDate(...datearr);
      return min;
    },

    /*
     *    [new Date(2017, 6, 24), new Date(2017, 5, 20), new Date(2017, 6, 26)] `max` is 2017-6-26
     */

    getMaxDate: function(datearr) {
      let max = date.getMaxDate(...datearr);
      return max;
    },

    /*
     *   params: (dateobj, datefromobj, datetoobj, inclusivefromdate bool, inclusivetodate bool)
     */
    isBetween: function(
      dateTarget,
      dateFrom,
      dateTo,
      inclusive = { inclusiveFrom: true, inclusiveTo: false }
    ) {
      return date.isBetweenDates(dateTarget, dateFrom, dateTo, inclusive);
    },

    /*
     *  params: (dateobj1 , dateobj2 , 'year' or 'month' or 'day' )
     *  if unit ommmited full date/comparison is performed
     */

    dateisEqual: function(date1, date2, /* optional */ unit) {
      return date.isSameDate(date1, date2, unit);
    },

    /*
     *  params: (dateobj1, dateobj2, /* optional  days by default )
     */

    dateDiff: function(date1, date2, unit = "days") {
      return date.getDateDiff(date1, date2, unit);
    },

    calendarget: function(dateobj, calendarmethod) {
      if (calendarmethod == "getweekofyear") {
        return date.getWeekOfYear(dateobj); // 2017,0,4 -> week is 1
      } else if (calendarmethod == "getdayofyear") {
        return date.getDayOfYear(dateobj); // 2017,1,4 -> day is 35
      } else if (calendarmethod == "getdayofweek") {
        return date.getDayOfWeek(dateobj); // 2017,1,9 -> day  is 4
      } else if (calendarmethod == "getdaysinmonth") {
        return date.daysInMonth(dateObj); // e.g 30
      }
    },

    /*
     * params: milliseconds
     *  Ex: timeConversion(12343392000000)  result 15328.0 Days
     */
    timeConversion: function(millisec) {
      var seconds = (millisec / 1000).toFixed(1);

      var minutes = (millisec / (1000 * 60)).toFixed(1);

      var hours = (millisec / (1000 * 60 * 60)).toFixed(1);

      var days = (millisec / (1000 * 60 * 60 * 24)).toFixed(1);

      if (seconds < 60) {
        return seconds + " Sec";
      } else if (minutes < 60) {
        return minutes + " Min";
      } else if (hours < 24) {
        return hours + " Hrs";
      } else {
        return days + " Days";
      }
    }
  }
};
