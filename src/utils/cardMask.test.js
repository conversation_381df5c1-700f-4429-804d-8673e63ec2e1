/**
 * Тесты для утилиты маскирования номеров карт
 */

// Копируем функцию для тестирования
function maskCardNumber(cardNumber) {
  try {
    // Проверяем, что значение существует
    if (!cardNumber && cardNumber !== 0) {
      return '';
    }

    // Преобразуем в строку, если это не строка
    const cardNumberStr = String(cardNumber);

    // Если строка слишком короткая, возвращаем как есть
    if (cardNumberStr.length < 11) {
      return cardNumberStr;
    }

    // Маскируем символы с 7 по 11 позицию (индексы 6-10)
    const firstPart = cardNumberStr.substring(0, 6);
    const lastPart = cardNumberStr.substring(11);
    const maskedMiddle = '*****'; // 5 звездочек для позиций 7-11

    return `${firstPart}${maskedMiddle}${lastPart}`;
  } catch (error) {
    console.warn('Ошибка при маскировании номера карты:', error);
    return '******';
  }
}

// Простые тесты для проверки функциональности
console.log('Тестирование функции maskCardNumber:');

// Тест 1: Обычный номер карты 16 цифр
const test1 = '****************';
const result1 = maskCardNumber(test1);
console.log(`Тест 1: ${test1} -> ${result1}`);
console.log(`Ожидается: 418973*****1234, получено: ${result1}`);
console.log(`Результат: ${result1 === '418973*****1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 2: Номер карты с пробелами (маскируем позиции 7-11)
const test2 = '4189 7345 6789 1234';
const result2 = maskCardNumber(test2);
console.log(`Тест 2: ${test2} -> ${result2}`);
console.log(`Ожидается: 4189 7*****789 1234, получено: ${result2}`);
console.log(`Результат: ${result2 === '4189 7*****789 1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 3: Номер карты с дефисами (маскируем позиции 7-11)
const test3 = '4189-7345-6789-1234';
const result3 = maskCardNumber(test3);
console.log(`Тест 3: ${test3} -> ${result3}`);
console.log(`Ожидается: 4189-7*****789-1234, получено: ${result3}`);
console.log(`Результат: ${result3 === '4189-7*****789-1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 4: Короткий номер (должен остаться без изменений)
const test4 = '123456789';
const result4 = maskCardNumber(test4);
console.log(`Тест 4: ${test4} -> ${result4}`);
console.log(`Ожидается: ${test4}, получено: ${result4}`);
console.log(`Результат: ${result4 === test4 ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 5: Пустая строка
const test5 = '';
const result5 = maskCardNumber(test5);
console.log(`Тест 5: "${test5}" -> "${result5}"`);
console.log(`Ожидается: "", получено: "${result5}"`);
console.log(`Результат: ${result5 === '' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 6: null
const test6 = null;
const result6 = maskCardNumber(test6);
console.log(`Тест 6: ${test6} -> ${result6}`);
console.log(`Ожидается: "", получено: "${result6}"`);
console.log(`Результат: ${result6 === '' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 7: Номер карты 15 цифр (American Express)
const test7 = '***************';
const result7 = maskCardNumber(test7);
console.log(`Тест 7: ${test7} -> ${result7}`);
console.log(`Ожидается: 378282*****0005, получено: ${result7}`);
console.log(`Результат: ${result7 === '378282*****0005' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 8: Номер карты как число (основная проблема)
const test8 = 4189733860;
const result8 = maskCardNumber(test8);
console.log(`Тест 8: ${test8} -> ${result8}`);
console.log(`Ожидается: ${test8}, получено: ${result8}`);
console.log(`Результат: ${result8 === String(test8) ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 9: Большое число как номер карты
const test9 = ****************;
const result9 = maskCardNumber(test9);
console.log(`Тест 9: ${test9} -> ${result9}`);
console.log(`Ожидается: 418973*****1234, получено: ${result9}`);
console.log(`Результат: ${result9 === '418973*****1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 10: Текст с арабскими символами
const test10 = 'أبجد٧٨٩٠١٢٣٤٥٦';
const result10 = maskCardNumber(test10);
console.log(`Тест 10: ${test10} -> ${result10}`);
console.log(`Ожидается: أبجد٧٨*****٣٤٥٦, получено: ${result10}`);
console.log(`Результат: ${result10 === 'أبجد٧٨*****٣٤٥٦' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

console.log('Тестирование завершено!');
