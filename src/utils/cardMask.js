/**
 * Утилита для маскирования номеров карт
 * Формат: 418973******3860 (первые 6 и последние 4 цифры видны)
 */

export function maskCardNumber(cardNumber) {
  // Маскируем символы с 3 по 7 позицию независимо от содержимого
  try {
    console.log('🔍 maskCardNumber вызвана с:', cardNumber, typeof cardNumber);

    // Проверяем, что значение существует
    if (!cardNumber && cardNumber !== 0) {
      console.log('❌ Пустое значение, возвращаем пустую строку');
      return '';
    }

    // Преобразуем в строку, если это не строка
    const cardNumberStr = String(cardNumber);
    console.log('📝 Преобразовано в строку:', cardNumberStr);

    // Если строка слишком короткая, возвращаем как есть
    if (cardNumberStr.length < 7) {
      console.log('⚠️ Строка слишком короткая, возвращаем как есть');
      return cardNumberStr;
    }

    // Маскируем символы с 3 по 7 позицию (индексы 2-6) - неважно что там
    const firstPart = cardNumberStr.substring(0, 3);
    const lastPart = cardNumberStr.substring(7);
    const maskedMiddle = '*****'; // 5 звездочек для позиций 3-7

    const result = `${firstPart}${maskedMiddle}${lastPart}`;
    console.log('✅ Результат маскирования:', result);
    return result;
  } catch (error) {
    // В случае любой ошибки возвращаем безопасную маскированную строку
    console.warn('❌ Ошибка при маскировании номера карты:', error);
    return '******';
  }
}

export default {
  maskCardNumber
};
