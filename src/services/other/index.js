import axios from "axios"

const ACCESS_TOKEN = 'L6JYMxnk6UAzXzMnvhxW'

export default {
    GET_PIPELINES () {
        return axios.get(process.env.NODE_ENV === 'development' ? 'https:///api/v4/projects/470/pipelines?status=success&ref=dev&order_by=updated_at' : 'https:///api/v4/projects/470/pipelines?status=success&ref=k8s&order_by=updated_at', { headers: { PRIVATE_TOKEN: ACCESS_TOKEN, "Content-Type": "application/json"}})
    },
}
