import { Api } from 'boot/axios'

export default {

    GET_EMPLOYEES_BLACKLIST(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`authorization/api/v2/clients-black-list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    UPDATE_CASE_BUTTON(payload) {
        return Api.put(`admin-panel/api/v1/hdbk/solution`, payload)
    },

    GET_WAY4_CARD_OPERATION_FREQUENCY_REPORT(start_date, end_date) {
        return Api.get(`authorization/api/v2/way4/card-operation-frequency?date_from=${start_date}&date_to=${end_date}`)
    },

    GET_WAY4_CARD_FREQUENCY_REPORT(start_date, end_date) {
        return Api.get(`authorization/api/v2/way4/card-frequency?date_from=${start_date}&date_to=${end_date}`)
    },

    GET_WAY4_ALL_TRANSACTIONS(start_date, end_date) {
        return Api.get(`authorization/api/v2/way4/list?date_from=${start_date}&date_to=${end_date}`)
    },

    EDIT_RULES10_PARAM(payload) {
        return Api.put(`financial-gap-service/api/v1/params`, payload)
    },

    GET_RULE_10_PARAM(page = 1, per_page = 100) {
        return Api.get(`financial-gap-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    GET_BLACK_LIST_CARDS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`admin-panel/api/v1/card-black-list/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    ADD_BLACK_LIST_CARD(payload) {
        return Api.post(`admin-panel/api/v1/card-black-list`, payload)
    },

    EDIT_BLACK_LIST_CARD(payload) {
        return Api.put(`admin-panel/api/v1/card-black-list`, payload)
    },

    DELETE_BLACK_LIST_CARD(id) {
        return Api.delete(`admin-panel/api/v1/card-black-list?id=${id}`)
    },

    GET_WAY4_INFO() {
        return Api.get(`authorization/api/v2/way4/list?date_from=25.08.2021T10:00:00&date_to=25.08.2021T10:00:10`)
    },

    ADD_EMAIL_NOTIFICATIONS(payload) {
        return Api.post(`authorization/api/v2/email-receiver`, payload)
    },

    EDIT_EMAIL_NOTIFICATIONS(payload) {
        return Api.put(`authorization/api/v2/email-receiver`, payload)
    },

    GET_EMAIL_LIST_NOTIFICATIONS() {
        return Api.get(`authorization/api/v2/email-receiver/list`)
    },

    DELETE_EMAIL_NOTIFICATION_BY_ID(id) {
        return Api.delete(`authorization/api/v2/email-receiver?id=${id}`)
    },

    GET_REPORT1(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-type-client-type-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT2(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-type-client-type-country-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT3(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-type-country-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT4(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-type-channel-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT5(start_date, end_date) {
        return Api.get(`authorization/api/v2/case-reg-auth-client-type-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT6(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-fin-oper-type-client-type-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT7(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-fin-oper-type-currency-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT8(start_date, end_date) {
        return Api.get(`authorization/api/v2/event-fin-oper-type-country-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT9(start_date, end_date) {
        return Api.get(`authorization/api/v2/case-type-status-count?start_date=${start_date}&end_date=${end_date}`)
    },

    GET_REPORT10(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date, end_date) {
        return Api.get(`authorization/api/v2/case-user-scenario-count?sort=${sort_order}&sort_field=case_date&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`)
    },

    GET_DJ_LIST(page = 1, per_page = 100, sort_field = "id", sort_order = "asc") {
        return Api.get(`authorization/api/v2/dow-jones?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}`)
    },

    GET_INTEGRATION_LIST(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`authorization/api/v2/system/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    EDIT_INTEGRATION_LIST(form) {
        return Api.put(`authorization/api/v2/system-open-status?open_status=${form.open_status}&system_id=${form.system_id}`)
    },

    GET_EVENT_TYPES_LIST(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/event_types/list?page=${page}&per_page=${per_page}`)
    },

    GET_HISTORYSTORAGE_SETTINGS(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/history/list?page=${page}&per_page=${per_page}`)
    },

    UPATE_HISTORYSTORAGE_SETTINGS(payload) {
        return Api.put(`admin-panel/api/v1/hdbk/history`, payload)
    },

    GET_SYSTEM_LIST(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/system/list?page=${page}&per_page=${per_page}`)
    },
    GET_SCORING_LIST(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`admin-panel/api/v1/scoring/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },
    ADD_SCORING_ITEM(form) {
        return Api.post(`admin-panel/api/v1/scoring`, form)
    },
    UPDATE_SCORING_ITEM(form) {
        return Api.put(`admin-panel/api/v1/scoring`, form)
    },
    DELETE_SCORING_ITEM(id) {
        return Api.delete(`admin-panel/api/v1/scoring/${id}`)
    },
    CHANGE_CASE_ASSIGNEE(form) {
        return Api.patch(`admin-panel/api/v1/case/assign`, form)
    },
    DELETE_COMMENT(id) {
        return Api.delete(`admin-panel/api/v1/case/comment/${id}`)
    },
    EDIT_CASE_COMMENT(comment) {
        return Api.put(`admin-panel/api/v1/case/comment`, comment)
    },
    GET_CASE_COMMENTS(case_id) {
        return Api.get(`admin-panel/api/v1/case/comment/${case_id}`)
    },
    ADD_CASE_COMMENT(comment) {
        return Api.post(`admin-panel/api/v1/case/comment`, comment)
    },
    DELETE_FILE_INFO_BY_FILE_ID(file_id) {
        return Api.delete(`file-manager/api/v1/file/${file_id}`)
    },
    GET_FILE_INFO_BY_FILE_ID(file_id) {
        return Api.get(`file-manager/api/v1/file/info/${file_id}`)
    },
    GET_PAYMENT_PROFILE_PARAMS(page = 1, per_page = 5) {
        return Api.get(`payment-on-edit-profile-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },
    EDIT_PAYMENT_PROFILE_PARAMS(payload) {
        return Api.put(`payment-on-edit-profile-service/api/v1/params`, payload)
    },
    GET_TRANSFER_LIMIT_SERVICE_PARAMS(page = 1, per_page = 5) {
        return Api.get(`transfer-limit-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },
    UPDATE_TRANSFER_LIMIT_SERVICE_PARAMS(payload) {
        return Api.put(`transfer-limit-service/api/v1/params`, payload)
    },
    GET_NOT_TYPICAL_AMOUNT_SERVICE_PARAMS(page = 1, per_page = 5) {
        return Api.get(`not-typical-amount-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },
    UPDATE_NOT_TYPICAL_AMOUNT_SERVICE_PARAMS(payload) {
        return Api.put(`not-typical-amount-service/api/v1/params`, payload)
    },
    GET_NOT_TYPYCAL_AMOUNT_SERVICE_PERCENT(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`not-typical-amount-service/api/v1/percent/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },
    ADD_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(payload) {
        return Api.post(`not-typical-amount-service/api/v1/percent`, payload)
    },
    UPDATE_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(payload) {
        return Api.put(`not-typical-amount-service/api/v1/percent`, payload)
    },
    DELETE_NOT_TYPICAL_AMOUNT_SERVICE_PERCENT(id) {
        return Api.delete(`not-typical-amount-service/api/v1/percent/${id}`)
    },
    GET_NEW_PAYEE(page = 1, per_page = 100) {
        return Api.get(`new-payee-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    UPDATE_NEW_PAYEE(payload) {
        return Api.put(`new-payee-service/api/v1/params`, payload)
    },

    GET_INACTIVE_ACC_PARAMAS(page = 1, per_page = 100) {
        return Api.get(`inactive-acc-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    EDIT_INACTIVE_ACC_PARAMS(payload) {
        return Api.put(`inactive-acc-service/api/v1/params`, payload)
    },

    GET_PAYMENT_TREND_PARAMS(page = 1, per_page = 100) {
        return Api.get(`payment-amount-trend/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    EDIT_PAYMENT_TREND_PARAMS(payload) {
        return Api.put(`payment-amount-trend/api/v1/params`, payload)
    },

    GET_NEW_CARD_TRANSACTION_PARAMS(page = 1, per_page = 100) {
        return Api.get(`new-card-transaction-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    EDIT_NEW_CARD_TRANSACTION_PARAMS(payload) {
        return Api.put(`new-card-transaction-service/api/v1/params`, payload)
    },

    GET_AGE_CHECKER_PARAMS(page = 1, per_page = 100) {
        return Api.get(`age-checker/api/v1/params?page=${page}&per_page=${per_page}`)
    },

    EDIT_AGE_CHECKER_PARAMS(payload) {
        return Api.put(`age-checker/api/v1/params`, payload)
    },

    GET_HDBK_PRODUCTS(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/product/list?page=${page}&per_page=${per_page}`)
    },

    GET_FINANCIAL_LIMIT_PARAMS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`financial-limit-service/api/v1/matrix/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    EDIT_FINANCIAL_LIMIT_PARAM(payload) {
        return Api.put(`financial-limit-service/api/v1/matrix`, payload)
    },

    DELETE_FINANCIAL_SPEED_PAYMENT_PARAM(id) {
        return Api.delete(`financial-speed-payments-service/api/v1/matrix/${id}`)
    },

    DELETE_FINANCIAL_LIMIT_PARAM(id) {
        return Api.delete(`financial-limit-service/api/v1/matrix/${id}`)
    },

    ADD_FINANCIAL_SPEED_PAYMENT_PARAM(payload) {
        return Api.post(`financial-speed-payments-service/api/v1/matrix`, payload)
    },

    ADD_FINANCIAL_LIMIT_PARAM(payload) {
        return Api.post(`financial-limit-service/api/v1/matrix`, payload)
    },

    GET_MULTIPLE_LOGIN_PARAMS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`multiple-login-service/api/v1/params?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    GET_FINANCIAL_SPEED_PAYMENT_PARAMS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`financial-speed-payments-service/api/v1/matrix/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    EDIT_FINANCIAL_SPEED_PAYMENT_PARAM(payload) {
        return Api.put(`financial-speed-payments-service/api/v1/matrix`, payload)
    },

    EDIT_LOGIN_PARAMS(payload) {
        return Api.put(`/multiple-login-service/api/v1/params`, payload)
    },

    GET_CASE_STATUSES(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/case_buttons?page=${page}&per_page=${per_page}`)
    },

    GET_HDBK_SOLUTION_LIST(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/solution/list?page=${page}&per_page=${per_page}`)
    },

    GET_CASE_STATUSES_SYSTEM(page = 1, per_page = 100) {
        return Api.get(`admin-panel/api/v1/hdbk/event_statuses?page=${page}&per_page=${per_page}`)
    },

    GET_ALL_CASES(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`admin-panel/api/v1/case/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=and&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },

    GET_ALL_CASES_DETAILED(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date = "", end_date = "") {
        return Api.get(`admin-panel/api/v1/case/cases-detailed?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`)
    },

    GET_EXPORT_EXCEL(start_date = "", end_date = "") {
      return Api.get(`admin-panel/api/v1/case/export?start_date=${start_date}&end_date=${end_date}`, {responseType: "blob"})
    },

    GET_ALL_SMP(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date = "", end_date = "") {
        return Api.get(`admin-panel/api/v1/smp/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`)
    },
    GET_SMP_EXPORT_EXCEL(start_date, end_date) {
        return Api.get(`admin-panel/api/v1/smp/export?start_date=${start_date}&end_date=${end_date}`, {responseType: 'blob'})
    },
    GET_ALL_MONEY_TRANSFERS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date = "", end_date = "") {
        return Api.get(`admin-panel/api/v1/money_transfers/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`)
    },
    GET_MONEY_TRANSFERS_EXPORT_EXCEL(start_date, end_date) {
        return Api.get(`admin-panel/api/v1/money_transfers/export?start_date=${start_date}&end_date=${end_date}`, {responseType: 'blob'})
    },

    GET_ALL_RELATED_TRANSFERS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date = "", end_date = "",iin="") {
        return Api.post(`admin-panel/api/v1/related-transfers/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`,{iin})
    },
    GET_ALL_PENSIONERS_TRANSFERS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "", start_date = "", end_date = "") {
        return Api.get(`admin-panel/api/v1/pensioner-opers/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}&start_date=${start_date}&end_date=${end_date}`)
    },

    GET_PENSIONER_EXPORT_EXCEL(start_date, end_date) {
        return Api.get(`admin-panel/api/v1/pensioner-opers/export?start_date=${start_date}&end_date=${end_date}`, {responseType: "blob"})
    },

    UPDATE_CASE(payload) {
        return Api.put(`/admin-panel/api/v1/case`, payload)
    },

    GET_CASE_EVENTS(case_id) {
        return Api.get(`/admin-panel/api/v1/event/case/${case_id}`)
    },
    GET_CASE_BY_ID(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`admin-panel/api/v1/case/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },
    GET_ALL_USERS() {
        return Api.get(`/authorization/api/v1/restricted/user/list?page=1&per_page=100`)
    },
    CHANGE_ROLE_BY_USERID(user_id, role_id) {
        return Api.put(`/authorization/api/v1/restricted/user/role?user_id=${user_id}&role_id=${role_id}`)
    },
    GET_ALL_ROLES() {
        return Api.get(`/authorization/api/v1/restricted/role/list?page=1&per_page=100`)
    },
    ADD_NEW_ROLE(role) {
        return Api.post(`/authorization/api/v1/restricted/role`, role)
    },
    DELETE_ROLE_BY_ID(id) {
        return Api.delete(`/authorization/api/v1/restricted/role?role_id=${id}`)
    },
    EDIT_ROLE(role) {
        return Api.put(`/authorization/api/v1/restricted/role`, role)
    },
    DELETE_USER_BY_ID(id) {
        return Api.delete(`/authorization/api/v1/restricted/user?user_id=${id}`)
    },
    EDIT_USER(user) {
        return Api.put(`/authorization/api/v1/restricted/user`, user)
    },
    GET_ALL_SCENARIOS(page = 1, per_page = 100, sort_field = "id", sort_order = "asc", filter_fields = "id", filter_values = "") {
        return Api.get(`/admin-panel/api/v1/scenario/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_type=like&filter_logic=or&filter_values=${filter_values}&filter_fields=${filter_fields}`)
    },
    EDIT_SCENARIO(scenario) {
        return Api.put(`/admin-panel/api/v1/scenario`, scenario)
    },
    GET_BLACKLIST_CLIENTS(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/internal/iin-blacklist/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_WHITELIST_CLIENTS(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/internal/iin-whitelist/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    EDIT_CLIENT_BLACKLIST(payload) {
        return Api.put(`/blacklist-service/api/v1/internal/iin-blacklist`, payload)
    },
    EDIT_CLIENT_WHITELIST(payload) {
        return Api.put(`/blacklist-service/api/v1/internal/iin-whitelist`, payload)
    },
    DELETE_CLIENT_BLACKLIST(id) {
        return Api.delete(`/blacklist-service/api/v1/internal/iin-blacklist/${id}`)
    },
    DELETE_CLIENT_WHITELIST(id) {
        return Api.delete(`/blacklist-service/api/v1/internal/iin-whitelist/${id}`)
    },
    ADD_CLIENT_TO_BLACKLIST(payload) {
        return Api.post(`/blacklist-service/api/v1/internal/iin-blacklist`, payload)
    },
    ADD_CLIENT_TO_WHITELIST(payload) {
        return Api.post(`/blacklist-service/api/v1/internal/iin-whitelist`, payload)
    },
    GET_IP_BLACKLIST(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/internal/ip-address?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_MAC_BLACKLIST(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/internal/mac-address/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    EDIT_IP_BLACKLIST(payload) {
        return Api.put(`/blacklist-service/api/v1/internal/ip-addresses/ip`, payload)
    },
    ADD_IP_BLACKLIST(payload) {
        return Api.post(`/blacklist-service/api/v1/internal/ip-addresses/ip`, payload)
    },
    DELETE_IP_BLACKLIST(id) {
        return Api.delete(`/blacklist-service/api/v1/internal/ip-addresses/ip/${id}`)
    },
    ADD_MAC_BLACKLIST(payload) {
        return Api.post(`/blacklist-service/api/v1/internal/mac-address`, payload)
    },
    EDIT_MAC_BLACKLIST(payload) {
        return Api.put(`/blacklist-service/api/v1/internal/mac-address`, payload)
    },
    DELETE_MAC_BLACKLIST(id) {
        return Api.delete(`/blacklist-service/api/v1/internal/mac-address/${id}`)
    },
    GET_PHONES_BLACKLIST(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/internal/phone/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    DELETE_PHONE_BLACKLIST(id) {
        return Api.delete(`/blacklist-service/api/v1/internal/phone/${id}`)
    },
    ADD_PHONE_BLACKLIST(payload) {
        return Api.post(`/blacklist-service/api/v1/internal/phone`, payload)
    },
    EDIT_PHONE_BLACKLIST(payload) {
        return Api.put(`/blacklist-service/api/v1/internal/phone`, payload)
    },
    GET_TALIBAN_TERRORISTS_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/taliban/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TALIBAN_TERRORISTS_LE(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/taliban/orgs/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORISTS_OON_LE(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/un/orgs/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORISTS_OON_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/un/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_RULE1_PARAM(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/params?page=${page}&per_page=${per_page}`)
    },
    GET_TERRORIST_SSI_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/ssi/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_SSI_LE(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/ssi/orgs/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_OFAC_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/ofac/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_OFAC_LE(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/ofac/orgs/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_KFM_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/kfm/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_KFM_LE(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/kfm/orgs/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_TERRORIST_ES_INDV(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/es/persons/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    GET_EXTERNAL_IP(page = 1, per_page = 100, sort_field = "", sort_order = "", filter_fields = "", filter_values = "") {
        return Api.get(`/blacklist-service/api/v1/external/ip-address/list?sort=${sort_order}&sort_field=${sort_field}&page=${page}&per_page=${per_page}&filter_values=${filter_values}&filter_fields=${filter_fields}&filter_type=like&filter_logic=or`)
    },
    EDIT_PARAMS_BLACKLIST(payload) {
        return Api.put(`/blacklist-service/api/v1/params`, payload)
    }
}
