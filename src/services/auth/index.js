import {Api} from 'boot/axios'

export default {
    SIGN_IN (payload) {
        return Api.post('authorization/api/v1/restricted/sign-up', payload)
    },
    LOGIN (payload) {
        return Api.post('authorization/api/v1/sign-in', payload)
    }, 
    CHANGE_PASS (payload) {
        return Api.post('authorization/api/v1/restricted/change-pass',payload)
    },
    RESET_PASSWORD (email) {
        return Api.post(`authorization/api/v1/reset-password?email=${email}`)
    },
}