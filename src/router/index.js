import Vue from "vue";
import VueRouter from "vue-router";
import { store } from "../store";
import routes from "./routes";

Vue.use(VueRouter);

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

const hasAccess = (name, { role }) => {

  switch (name) {
    case "login":
      return true;
    case "customer_management":
      return role.id == 1;
    case "roles_management":
      return role.id == 1;
    case "mac":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "ip":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "phone":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "incidents":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "dashboard":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "cases":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "casesatwork":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "case_details":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "money_transfers":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "smp":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "pensioners_transfers":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "profile":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "scenarios_management":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "clientsblacklist":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "clientswhitelist":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules1":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules2":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules3":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules4":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules5":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules6":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules7":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules8":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules9":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules10":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules11":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules12":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "rules13":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "scoring":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "dowjones":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "integration":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristlisttalibanindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristlisttalibanle":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristoonindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristoonle":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristssile":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristssiindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristofacindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristofacle":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristkfmindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristkfmle":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "terroristesindv":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "ipexternal":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "reports":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "emailnotifications":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "datastorage":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "reportsall4onepage":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "casestatusessettings":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "employeesblacklist":
      return role.id == 2 || role.id == 1 || role.id == 3;
    case "notfound":
      return true;
    default:
      return false;
  }
};

export default function (/* { store, ssrContext } */) {
  const Router = new VueRouter({
    scrollBehavior: () => ({ x: 0, y: 0 }),
    routes,
    mode: process.env.VUE_ROUTER_MODE,
    base: process.env.VUE_ROUTER_BASE
  });

  Router.beforeEach((to, from, next) => {
    if (
      (!localStorage.getItem("token") ||
        !Object.keys(store.state.auth.currentUser).length) &&
      to.name !== "login"
    ) {
      next({
        name: "login"
      });
    } else if (
      localStorage.getItem("token") &&
      Object.keys(store.state.auth.currentUser).length &&
      to.name == "login"
    ) {
      next({ name: "cases" });
    } else {
      if (hasAccess(to.name, store.state.auth.currentUser)) {
        next();
      } else {
        next({ name: "notfound" });
      }
    }
  });

  return Router;
}
