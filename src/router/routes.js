const routes = [
  { path: "/", component: () => import("pages/login.vue"), name: "login" },
  {
    path: "/",
    component: () => import("layouts/MainLayout.vue"),
    children: [
      { path: "/dashboard", component: () => import("pages/dashboard.vue"), name: "dashboard" },
      {
        path: "/customer_management",
        component: () => import("pages/customer_management.vue"),
        name: "customer_management"
      },
      {
        path: "/incidents",
        component: () => import("pages/incidents.vue"),
        name: "incidents"
      },
      {
        path: "/roles_management",
        component: () => import("pages/roles_management.vue"),
        name: "roles_management"
      },
      {
        path: "/scenarios_management",
        component: () => import("pages/scenarios.vue"),
        name: "scenarios_management"
      },
      {
        path: "/scoring",
        component: () => import("pages/scoring.vue"),
        name: "scoring"
      },
      {
        path: "/dowjones",
        component: () => import("pages/dowjones.vue"),
        name: "dowjones"
      },
      {
        path: "/integration",
        component: () => import("pages/integration.vue"),
        name: "integration"
      },
      {
        path: "/casestatusessettings",
        component: () => import("pages/casestatusessettings.vue"),
        name: "casestatusessettings"
      },
      {
        path: "/reportsall4onepage",
        component: () => import("pages/reportsall4onepage"),
        name: "reportsall4onepage"
      },
      {
        path: "/rules2",
        component: () => import('pages/rules2.vue'),
        name: "rules2"
      },
      {
        path: "/rules3",
        component: () => import('pages/rules3.vue'),
        name: "rules3"
      },
      {
        path: "/rules4",
        component: () => import('pages/rules4.vue'),
        name: "rules4"
      },
      {
        path: "/rules5",
        component: () => import('pages/rules5.vue'),
        name: "rules5"
      },
      {
        path: "/rules6",
        component: () => import('pages/rules6.vue'),
        name: "rules6"
      },
      {
        path: "/rules7",
        component: () => import('pages/rules7.vue'),
        name: "rules7"
      },
      {
        path: "/rules8",
        component: () => import('pages/rules8.vue'),
        name: "rules8"
      },
      {
        path: "/rules9",
        component: () => import('pages/rules9.vue'),
        name: "rules9"
      },
      {
        path: "/rules10",
        component: () => import('pages/rules10.vue'),
        name: "rules10"
      },
      {
        path: "/rules11",
        component: () => import('pages/rules11.vue'),
        name: "rules11"
      },
      {
        path: "/rules12",
        component: () => import('pages/rules12.vue'),
        name: "rules12"
      },
      {
        path: "/rules13",
        component: () => import('pages/rules13.vue'),
        name: "rules13"
      },
      {
        path: "/rules1",
        component: () => import('pages/rules1.vue'),
        name: "rules1",
        children: [
          {
            path: "11",
            component: () => import('pages/clientsblacklist.vue'),
            name: "clientsblacklist"
          },
          {
            path: "4",
            component: () => import('pages/clientswhitelist.vue'),
            name: "clientswhitelist"
          },
          {
            path: "3",
            component: () => import('pages/phone.vue'),
            name: "phone"
          },
          {
            path: "12",
            component: () => import('pages/ip.vue'),
            name: "ip"
          },
          {
            path: "2",
            component: () => import('pages/mac.vue'),
            name: "mac"
          },
          {
            path: "5",
            component: () => import('pages/terroristlisttalibanindv.vue'),
            name: "terroristlisttalibanindv"
          },
          {
            path: "14",
            component: () => import('pages/terroristlisttalibanle.vue'),
            name: "terroristlisttalibanle"
          },
          {
            path: "8",
            component: () => import('pages/terroristoonindv.vue'),
            name: "terroristoonindv"
          },
          {
            path: "17",
            component: () => import('pages/terroristoonle.vue'),
            name: "terroristoonle"
          },
          {
            path: "16",
            component: () => import('pages/terroristssile.vue'),
            name: "terroristssile"
          },
          {
            path: "7",
            component: () => import('pages/terroristssiindv.vue'),
            name: "terroristssiindv"
          },
          {
            path: "9",
            component: () => import('pages/terroristofacindv.vue'),
            name: "terroristofacindv"
          },
          {
            path: "18",
            component: () => import('pages/terroristofacle.vue'),
            name: "terroristofacle"
          },
          {
            path: "6",
            component: () => import('pages/terroristkfmindv.vue'),
            name: "terroristkfmindv"
          },
          {
            path: "15",
            component: () => import('pages/terroristkfmle.vue'),
            name: "terroristkfmle"
          }, {
            path: "10",
            component: () => import('pages/terroristesindv.vue'),
            name: "terroristesindv"
          },
          {
            path: "1",
            component: () => import('pages/ipexternal.vue'),
            name: "ipexternal"
          }, {
            path: "21",
            component: () => import('pages/employeesblacklist.vue'),
            name: "employeesblacklist"
          }
        ]
      },
      {
        path: "/emailnotifications",
        name: "emailnotifications",
        component: () => import("pages/notificationemail.vue")
      },
      {
        path: "/datastorage",
        name: "datastorage",
        component: () => import("pages/datastorage.vue")
      },
      {
        path: "/reports",
        component: () => import("pages/reports.vue"),
        name: "reports"
      },
      {
        path: "/case_details/:case_id",
        component: () => import("pages/case_details.vue"),
        name: "case_details"
      },
      {
        path: "/smp",
        component: () => import("pages/smp.vue"),
        name: "smp"
      },
      {
        path: "/money_transfers",
        component: () => import("pages/money_transfers.vue"),
        name: "money_transfers"
      },
      {
        path: "/pensioners_transfers",
        component: () => import("pages/pensioners_transfers.vue"),
        name: "pensioners_transfers"
      },
      {
        path: "/casesatwork",
        component: () => import("pages/casesatwork.vue"),
        name: "casesatwork"
      },
      {
        path: "/cases",
        component: () => import("pages/cases.vue"),
        name: "cases"
      },
      { path: "/my_profile", component: () => import("pages/my_profile.vue"), name: "profile" },
      {
        path: '/404',
        name: "notfound",
        component: () => import('../pages/Error404.vue'),
      }, {
        path: '*',
        redirect: '/404'
      }
    ]
  }
];

export default routes;
