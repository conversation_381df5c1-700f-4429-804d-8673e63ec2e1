export default {
  stopEvent: "Остановить событие",
  cases: "Управление инцидентами",
  allCases: "Все кейсы",
  smp: "СМП",
  money_transfers: "Переводы",
  customer_transactions: "Транзакции клиента",
  pensioners_transfers: "Переводы пенсионеров",
  casesStopped: "Приостановленные",
  casesWork: "Рабочий стол оператора",
  recordNotFound: "Записей не найдено",
  users: "Пользователи",
  rolesDict: "Справочник Ролей",
  analytics: "Аналитика",
  settings: "Настройки",
  ruleConfigurations: "Конфигурация правил",
  profile: "Профиль",
  mainMenu: "Главное Меню",
  integrationChannelSettings: "Настройки каналов интеграций",
  statusSettings: "Настройки статусов",
  versionSettings: "Настройки версий",
  notificationSettings: "Настройки уведомлений",
  storageSettings: "Настройки хранения данных",
  djListSettings: "Списки DJ",
  allRules: "Все правила",
  rule: "Правило",
  // Список кейсов
  caseId: "ID кейса",
  casedateecreated: "Дата и время создания",
  full_name:"ФИО",
  amount_kzt:"Сумма",
  product_id:"Идентификатор продукта",
  casedateclosed: "Дата и время закрытия",
  caseduedate: "Срок исполнения",
  score: "Скоринг балл",
  riskLevel: "Уровень риска",
  user: "Пользователь",
  status: "Статус",
  low: "Низкий",
  middle: "Средний",
  high: "Высокий",
  allowed: "Разрешено",
  suspended: "Приостановлено",
  forbidden: "Запрещено",
  onanalys: "На анализе",
  unabletocheck:
    "Проверка невозможна (неполная информация, техническая ошибка)",
  search: "Поиск",
  // Детали кейса
  filter: "Фильтр",
  goBack: "Вернуться Назад",
  caseDetails: "Детали кейса",
  casePriority: "Приоритет кейса",
  workerStatus: "Статус сотрудника",
  workerInitials: "ФИО Сотрудника",
  value: "Значение",
  measurement: "Еденица измерения",
  change: "Изменить",
  add: "Добавить",
  changeValue: "Изменить значение",
  addValue: "Добавить значение",
  systemStatus: "Системный статус",
  workedRules: "Сработавшие правила",
  columnName: "Название",
  columnDescription: "Описание",
  columnScore: "Балл",
  workerComment: "Комментарий Оператора",
  workerCommentPlaceholder: "Введите текст сообщения",
  uploadDocument: "Загрузить документ",
  changeComment: "Изменить комментарий",
  changeAssignedUser: "Изменить ответсвенного",
  dueDate: "Срок",
  identificationDetails: "Идентификационные данные",
  userDetails: "Данные о пользователе",
  eventDetails: "Данные события",
  operationDetails: "Данные об операций",
  creditDebitDetails: "Данные по кредитовой или дебетовой стороне операций",
  save: "Сохранить",
  close: "Закрыть",
  chooseStatus: "Выбор статуса",
  // Идентификационные данные case_details
  browserSessionId: "Уникальный идентификатор сессии браузера",
  userAgentInfo: "Данные из клиентского User Agent",
  ip4Client: "Ip-адрес клиентского устройства",
  ip6Client: "Ipv6-адрес клиентского устройства",
  physicalAddressClient: "Физический адрес клиентского устройства",
  deviceImei: "Идентификатор мобильного устройства (IMEI)",
  country: "Страна",
  region: "Регион/Область",
  city: "Город",
  zip: "Почтовый индекс",
  // Данные о пользователе
  iinbin: "ИИН/БИН",
  userSystemId: "Идентификатор участника в системе",
  isUserOrganizationClient: "Является ли участник клиентом организации",
  clientRequestProcessed: "Дата принятия клиента на обслуживание",
  clientTypeForm: "Тип клиента (форма собственности)",
  clientRole: "Роль участника",
  orgLegalForm: "Организационно-правовая форма",
  name: "Имя",
  patronymic: "Отчество",
  lastname: "Фамилия",
  shortname: "Сокращенное_название",
  documentType: "Тип документа",
  documentNumber: "Номер документа",
  region: "Регион",
  district: "Район",
  cityandlivingplace: "Город / населенный пункт",
  streetandmicrodistrict: "Улица/Микрорайон",
  homeandbuilding: "Дом/Здание",
  apartmentandoffice: "Квартира/Офис",
  oldPostalIndex: "Старый почтовый индекс",
  postalIndex: "Почтовый индекс",
  codePKA: "Код РКА",
  phoneNumber: "Номер телефона",
  birthdateAndRegistration: "Дата рождения/регистрации",
  citizenship: "Гражданство",
  countryResidence: "Страна резидентства",
  countryBirthAndRegistration: "Страна рождения/регистрации",
  //  Данные события
  login: "Логин",
  mobileNumber: "Мобильный номер",
  error: "Ошибка",
  // Данные об операций
  uniqueOperationId: "Уникальный идентификатор операции",
  operationType: "Тип операции",
  operationSubType: "Подтип операции",
  operationCreationDate: "Дата и время создания операции",
  knpCode: "КНП",
  kbkCode: "КБК",
  knoCode: "КНО",
  productGroupId: "ID Группы продуктов",
  productId: "ID Продукта",
  serviceId: "ID Услуги",
  paymentAssignment: "Назначение платежа",
  openRefCode: "Код отделения, проводящего операцию",
  // Данные по кредитовой или дебетовой стороне операций
  currencyCode: "Валюта в трехзначном коде",
  currencyAmount: "Cумма в валюте",
  currencyAmountKzt: "Сумма в тенге",
  debitCredit: "Дебет/Кредит",
  mcc: "MCC",
  cardType: "Тип карты",
  cardOwner: "Владелец карты",
  cardNumberMasked: "Маскированный номер карты",
  cardOpenDate: "Дата открытия карты",
  cardExpDate: "Срок действия карты",
  bankBic: "БИК Банка",
  bankName: "Наименование Банка",
  gkAccount: "Счет ГК",
  serviceOperatorCode: "Код поставщика услуг",
  fiscalType: "Тип лицевого счета",
  fiscalNumber: "Номер лицевого счета",
  // Настройки каналов интеграций
  integrationChannels: "Каналы интеграций",
  // Настройки статусов
  registration: "Регистрация",
  loginAuthorization: "Авторизация по логину",
  loginMobilePhone: "Авторизация по моб. телефону",
  finOperation: "Фин. операция",
  profileChange: "Изменение профиля",
  minimalScore: "Минимальный балл",
  maximumScore: "Максимальный балл",
  channel: "Канал",
  eventType: "Тип события",
  decision: "Решение",
  // Настройки версий
  version: "Версия",
  // Настройка уведомлений
  email: "Почта",
  // Настройки хранения данных
  finOperationFull: "Финансовые Операций",
  userActions: "Действия Пользователей",
  registrationandauthorization: "Регистрация/Авторизация",
  // Списки DJ
  fio: "ФИО",
  dictionary: "Справочник",
  comment: "Комментарий",
  birthdate: "Дата рождения",
  occupationType: "Вид деятельности",
  occupationStartDate: "Дата начала деятельности",
  djId: "Идентификатор DJ",
  gender: "Пол",
  aliveOrDeceased: "Мертв/Жив",
  active: "Активен",
  updateDate: "Дата обновления",
  // Все правила
  scenarioName: "Название сценария",
  onOrOff: "Включен/выключен",
  sendResultSysInit: "Отправлять результат с/и",
  shouldCreateCase: "Создавать кейс",
  changeScenario: "Изменить сценарий",
  // Правило 1
  order: "Порядок",
  percentageMatch: "Процент совпадения",
  on: "Включен",
  editing: "Редактирование",
  // Правило 2
  timeInterval: "Временной интервал",
  eventsAmount: "Количество событий",
  eventsAmountShort: "Кол соб",
  // Правило 3
  operationView: "Вид операции",
  clientType: "Тип клиента",
  sumLimit: "Лимит по сумме",
  scoreResult: "Скор-балл",
  operationLimit: "Лимит по количеству операций",
  period: "Период",
  // timeOptions
  year: "Год",
  years: "Года",
  years2: "Лет",
  day: "День",
  days: "Дня",
  days2: "Дней",
  month: "Месяц",
  months: "Месяца",
  months2: "Месяцов",
  hour: "Час",
  hours: "Часа",
  hours2: "Часов",
  minute: "Минута",
  minutes: "Минуты",
  minutes2: "Минут",
  second: "Секунда",
  seconds: "Секунды",
  seconds2: "Секунд",
  // Правило 6
  sumThreshold: "Пороговая сумма",
  // Правило 7
  percent: "Процент",
  // Правило 8
  receiversCount: "Количество Получателей",
  // Правило 9
  blackCardListSettings: "Настройки черного списка карт",
  // Правило 12
  ageChecker: {
    "DefaultScore": "Балл",
    "MinAge": "Возраст совершеннолетия",
    "MaxManAge": "Мужской пенсионный возраст",
    "MaxWomanAge": "Женский пенсионный возраст",
    "MaximumAmountForMinors":"Максимальная сумма для несовершеннолетних",
    "PeriodForMinors":"Период транзакций для несовершеннолетних",
    "CountTransactionsForMinors":"Колличество транзакций для несовершеннолетних"
  },
  // Правило 13
  newCardTransaction: {
    "DefaultScore": "Балл",
    "AmountLimit": "Предел суммы",
    "PeriodInDays": "Период в днях",
    "AgeLimit": "Предельный возраст",
    "TransactionAmountLimit":"Предел суммы для транзакции",
  },


  cardNumber: "   Номер карты",
  // Аналитика
  reportType: "Тип отчета",
  datefrom: "Дата с",
  dateto: "Дата по",
  report1:
    "Количество событий и количество кейсов в разрезе типов событий и типов клиентов",
  report2:
    "Количество событий и количество кейсов в разрезе стран и типов событий",
  report3: "Количество событий и количество кейсов в разрезе каналов",
  report4:
    "Количество событий и количество кейсов в разрезе типов клиентов и стран",
  report5:
    'Количество кейсов по событиям "регистрация" и "авторизация" в разрезе типов клиентов и статусов',
  report6:
    'Количественные показатели события "финансовая операция" в разрезе видов операций и типов клиента',
  report7:
    'Количественные показатели события "финансовая операция" в разрезе видов операций и валют',
  report8:
    'Количественные показатели события "финансовая операция" в разрезе стран и видов операций',
  report9: "Количество кейсов в разрезе типов событий и статусов",
  report10: "Кейсы из старого антифрода",
  report11: "Частота операций проводимых по одной и той же карте",
  report12: "Частота операций проводимых по одной и той же карте #2",
  report13: "Все транзакций",
  report14: "Большие суммы",
  clientId: "ID Клиента",
  fullName: "Полное Имя",
  caseCreationDate: "Дата создания кейса",
  operationFrequency: "Частота операций",
  ourCardsAtUs: "Наши карты у нас",
  ourCardsDifferentNetwork: "Наши карты в чужой сети",
  bvuCardsOurs: "Карты БВУ у нас",
  downloadDate: "Дата выгрузки",
  userIdentification: "Идентификатор пользователя",
  sourceNumber: "Номер источника",
  authorizationCode: "Код авторизаций",
  transactionCountry: "Страна проведенной транзакций",
  transactionCity: "Город проведенной транзакций",
  transactionDetails: "Детали транзакций",
  transactionDate: "Дата",
  transactionAmount: "Сумма",
  transactionStatus: "Статус транзакций",
  currency: "Валюта",
  merchantName: "Наименование торговца",
  caseCount: "Количество кейсов",
  caseCountShort: "Кол кейс",
  authorization: "Авторизация",
  finShortcaseCount: "Фин",
  finOperShort: "Фин операция",
  finOper: "Финансовая операция",
  // Профиль
  newPassword: "Новый Пароль",
  oldPassword: "Старый Пароль",
  wordsInLowRegister: "Буквы в нижнем регистре",
  wordsInHigherRegister: "Буквы в верхнем регистре",
  numbers: "Номера",
  min8Symbols: "Минимально 8 символов",
  requiredField: "Поле обязательное",
  passwordMustContainAtLeast8Symbold:
    "Пароль должен содержать не менее 8 символов",
  role: "Роль",
  update: "Обновить",
  // Пользователи
  changeUser: "Изменить пользователя",
  addNewUser: "Добавить нового пользователя",
  addUser: "Добавить пользователя",
  // Справочник ролей
  addNewRole: "Добавить новую роль",
  addRole: "Добавить роль",
  roleNameRu: "Название Роли Рус",
  roleNameEng: "Название Роли Англ",
  roleNameKz: "Название Роли Каз",
  changeRole: "Изменить роль",
  roles: "Роли",
  // Клиенты белого списка
  clientswhitelist: "Клиенты белого списка",
  changeClient: "Изменить Клиента",
  notes: "Примечание",
  addClient: "Добавить Клиента",
  // IP адреса из внешнего 'черного списка
  ipaddressexternalblacklist: "IP адреса из внешнего 'черного списка",
  // IP адреса черный список
  ipadressblacklist: "Черный список сотрудников",
  changeIp: "Изменить IP",
  ipAddress: "IP адрес",
  addIp: "Добавить IP",
  // MAC адреса черный список
  macAddressesBlackList: "MAC адреса черный список",
  changeMacAddress: "Изменить MAC",
  macAddress: "MAC адрес",
  addMacAddress: "Добавить MAC",
  // Террористы талибан физ
  terroristTalibanIndv: "Террористы талибан физ",
  number: "Номер",
  firstName: "Первое Имя",
  secondName: "Второе Имя",
  thirdName: "Третье Имя",
  fourthName: "Четвертое Имя",
  //  Террористы КФМ юр
  terroristKfmLe: "Террористы КФМ юр",
  orgNameEng: "Организационное Имя Англ",
  orgName: "Организационное Имя",
  // Террористы КФМ физ
  terroristKfmIndv: "Террористы КФМ физ",
  firstNameEng: "Первое Имя Англ",
  lastnameEng: "Фамилия Англ",
  iin: "ИИН",
  middleName: "Среднее Имя",
  middleNameEng: "Среднее Имя Англ",
  // Террористы SSI юр
  terroristSsiLe: "Террористы SSI юр",
  // Террористы Ssi физ
  terroristSsiIndv: "Террористы Ssi физ",
  // Террористы ООН юр
  terroristOonLe: "Террористы ООН юр",
  // Террористы ООН физ
  terroristOonIndv: "Террористы ООН физ",
  // Террористы ОФАК юр
  terroristOfacLe: "Террористы ОФАК юр",
  // Террористы ОФАК физ
  terroristOfacIndv: "Террористы ОФАК физ",
  title: "Заглавие",
  // Террористы ЕС физ.лица
  terroristEsIndv: "Террористы ЕС физ.лица",
  fullNameEng: "Полное Имя Англ",
  // Номер телефона черный список
  phoneNumbersBlackList: "Номер телефона черный список",
  phone: "Телефон",
  addPhoneNumber: "Добавить Номер Телефона",
  editPhoneNumber: "Изменить Номер Телефона",
  // Террористы талибан юр
  terroristTalibanLe: "Террористы талибан юр",
  // Клиенты черного списка
  clientsBlackList: "Клиенты черного списка",

  //
  caseStatusesSettings: "Настройки статусов кейсов",
  disabled: "Отключен",
  yes: "Да",
  no: "Нет",

  // datemixin
  sunday: "В Воскресенье",
  sundayShort: "Вс",
  monday: "В Понедельник",
  mondayShort: "Пн",
  tuesday: "Во вторник",
  tuesdayShort: "Вт",
  wednesday: "В Среду",
  wednesdayShort: "Ср",
  thursday: "В Четверг",
  thursdayShort: "Чт",
  friday: "В Пятницу",
  fridayShort: "Пт",
  saturday: "В Субботу",
  saturdayShort: "Сб",
  january: "Январь",
  february: "Февраль",
  march: "Март",
  april: "Апрель",
  may: "Мая",
  june: "Июня",
  july: "Июля",
  august: "Августа",
  september: "Сентябрь",
  october: "Октябрь",
  november: "Ноябрь",
  december: "Декабрь",
  janShort: "янв",
  febShort: "фев",
  marchShort: "мар",
  aprilShort: "апр",
  mayShort: "май",
  juneShort: "июнь",
  julyShort: "июль",
  augustShort: "авг",
  septemberShort: "сен",
  octoberShort: "окт",
  novemberShort: "ноя",
  decemberShort: "дек",


  // fl ul ip
  fl: "ФЛ",
  ul: "ЮЛ",
  ep: "ИП",

  report2eventsAmount: "Кол соб",
  report2caseAmount: "Кол кейс",
};
