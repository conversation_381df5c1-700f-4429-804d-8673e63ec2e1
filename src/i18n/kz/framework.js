function plurals (n, opts) {
    return opts[ n % 10 === 1 && n % 100 !== 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2 ]
  }
  
  export default {
    isoName: 'kz',
    nativeName: 'казахский',
    label: {
      clear: 'Таза',
      ok: 'OK',
      cancel: 'Болдырмау',
      close: 'Жабық',
      set: 'Орнату',
      select: 'Таңдаңыз',
      reset: 'Қалпына келтіру',
      remove: 'Жою',
      update: 'Жаңарту',
      create: 'Жасау',
      search: 'Іздеу',
      filter: 'Сүзгі',
      refresh: 'Жаңарту'
    },
    // date: {
    //     days: [
    //         this.$t("sunday"),
    //         this.$t("monday"),
    //         this.$t("tuesday"),
    //         this.$t("wednesday"),
    //         this.$t("thursday"),
    //         this.$t("friday"),
    //         this.$t('saturday')
    //       ],
    //       daysShort: [
    //         this.$t("sundayShort"),
    //         this.$t("mondayShort"),
    //         this.$t("tuesdayShort"),
    //         this.$t("wednesdayShort"),
    //         this.$t("thursdayShort"),
    //         this.$t("fridayShort"),
    //         this.$t('saturdayShort')
    //       ],
    //       months: [
    //         this.$t("january"),
    //         this.$t("february"),
    //         this.$t("march"),
    //         this.$t("april"),
    //         this.$t("may"),
    //         this.$t("june"),
    //         this.$t("july"),
    //         this.$t("august"),
    //         this.$t("september"),
    //         this.$t("october"),
    //         this.$t("november"),
    //         this.$t("december")
    //       ],
    //       monthsShort: [
    //         this.$t("janShort"),
    //         this.$t("febShort"),
    //         this.$t("marchShort"),
    //         this.$t("aprilShort"),
    //         this.$t("mayShort"),
    //         this.$t("juneShort"),
    //         this.$t("julyShort"),
    //         this.$t("augustShort"),
    //         this.$t("septemberShort"),
    //         this.$t("octoberShort"),
    //         this.$t("novemberShort"),
    //         this.$t("decemberShort")
    //       ],
    //       firstDayOfWeek: 1,
    //   format24h: true,
    //   pluralDay: 'күндер'
    // },
    table: {
      noData: 'Деректер жоқ',
      noResults: 'Сәйкестік табылмады',
      loading: 'Жүктелуде...',
      selectedRecords: rows => (
        rows > 0
          ? rows + ' ' + plurals(rows, [ 'жол таңдалды', 'жолдар таңдалды', 'жолдар таңдалды' ]) + '.'
          : 'Ешбір жол таңдалмаған.'
      ),
      recordsPerPage: 'Беттегі жолдар саны:',
      allRows: 'Беттегі жолдар саны:',
      pagination: (start, end, total) => start + '-' + end + ' бастап ' + total,
      columns: 'Баған'
    },
    editor: {
      url: 'URL',
      bold: 'Қалың',
      italic: 'Курсив',
      strikethrough: 'Сызық',
      underline: 'Асты сызылған',
      unorderedList: 'Маркированный список',
      orderedList: 'Нумерованный список',
      subscript: 'Подстрочный',
      superscript: 'Надстрочный',
      hyperlink: 'Гиперссылка',
      toggleFullscreen: 'Полноэкранный режим',
      quote: 'Цитата',
      left: 'Выравнивание по левому краю',
      center: 'Выравнивание по центру',
      right: 'Выравнивание по правому краю',
      justify: 'Выравнивание по ширине',
      print: 'Печать',
      outdent: 'Уменьшить отступ',
      indent: 'Увеличить отступ',
      removeFormat: 'Удалить форматирование',
      formatting: 'Форматирование',
      fontSize: 'Размер шрифта',
      align: 'Выравнивание',
      hr: 'Вставить горизонтальную линию',
      undo: 'Отменить',
      redo: 'Повторить',
      heading1: 'Заголовок 1',
      heading2: 'Заголовок 2',
      heading3: 'Заголовок 3',
      heading4: 'Заголовок 4',
      heading5: 'Заголовок 5',
      heading6: 'Заголовок 6',
      paragraph: 'Параграф',
      code: 'Код',
      size1: 'Очень маленький',
      size2: 'Маленький',
      size3: 'Нормальный',
      size4: 'Средний',
      size5: 'Большой',
      size6: 'Очень большой',
      size7: 'Огромный',
      defaultFont: 'Шрифт по умолчанию',
      viewSource: 'Просмотреть исходный код'
    },
    tree: {
      noNodes: 'Қолжетімді түйіндер жоқ',
      noResults: 'Сәйкестік табылмады'
    }
  }