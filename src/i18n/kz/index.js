export default {
  stopEvent: "Оқиғаны тоқтату",
  cases: "Оқиғаларды басқару",
  allCases: "Барлық жағдайлар",
  smp: "СМ<PERSON>",
  money_transfers: "Аударымдар",
  customer_transactions: "Клиенттің транзакциялары",
  pensioners_transfers: "Зейнеткерлерді аударымдар",
  casesStopped: "Тоқтатылды",
  casesWork: "Оператор үстелі",
  recordNotFound: "Жазбалар табылмады",
  users: "Пайдаланушылар",
  rolesDict: "Рөл каталогы",
  analytics: "Аналитика",
  settings: "Параметрлер",
  ruleConfigurations: "Ереже конфигурациясы",
  profile: "Профиль",
  // --
  mainMenu: "Басты мәзір",
  integrationChannelSettings: "Интеграциялық арналардың параметрлері",
  statusSettings: "Күй параметрлері",
  versionSettings: "Нұсқа параметрлері",
  notificationSettings: "Хабарландыру параметрлері",
  storageSettings: "Деректерді сақтау параметерлері",
  djListSettings: "DJ тізімдері",
  allRules: "Барлық ережелер",
  rule: "Ереже",
  // --
  caseId: "Жағдай ID",
  casedateecreated: "Жасалған күні мен уақыты",
  full_name:"ТАӘ",
  amount_kzt:"Сомасы",
  product_id:"Өнім сәйкестендіргіші",
  casedateclosed: "Жабылу күні мен уақыты",
  caseduedate: "Орындау уақыты",
  score: "Ұпай саны",
  riskLevel: "Тәуекел деңгейі",
  user: "Пайдаланушы",
  status: "Күй",
  low: "Төмен",
  middle: "Орташа",
  high: "Жоғары",
  allowed: "Рұқсат",
  suspended: "Уақытша тоқтатылды",
  forbidden: "Тыйым салынған",
  onanalys: "Талдау бойынша",
  unabletocheck: "Тексеру мүмкін емес (толық емес ақпарат, техникалық қате)",
  search: "Іздеу",
  filter: "Сүзгі",
  goBack: "Қайту",
  caseDetails: "Жағдай мәліметтері",
  casePriority: "Істің басымдығы",
  workerStatus: "Қызметкер мәртебесі",
  workerInitials: "Қызметкердің аты-жөні",
  value: "Мағынасы",
  measurement: "Өлшем бірлігі",
  change: "Өзгерту",
  add: "Қосу",
  changeValue: "Мәнді өзгерту",
  addValue: "Мән қосу",
  systemStatus: "Жүйе күйі",
  workedRules: "Іске қосылған ережелер",
  columnName: "Аты",
  columnDescription: "Сипаттама",
  columnScore: "Ұпай",
  workerComment: "Оператордың түсініктемесі",
  workerCommentPlaceholder: "Хабарламаңызды енгізіңіз",
  uploadDocument: "Құжатты жүктеп салу",
  changeComment: "Пікірді өңдеу",
  changeAssignedUser: "Жауапкершілікті өзгертіңіз",
  dueDate: "Мерзімі",
  identificationDetails: "Сәйкестендіру деректері",
  userDetails: "Пайдаланушы деректері",
  eventDetails: "Оқиға деректері",
  operationDetails: "Операциялық деректер",
  creditDebitDetails:
    "Мәмілелердің кредиттік немесе дебеттік жағы туралы деректер",
  save: "Сақтау",
  close: "Жабу",
  chooseStatus: "Күй таңдау",
  // Идентификационные данные case_details доделать
  browserSessionId: "Браузер сеансының бірегей идентификаторы",
  userAgentInfo: "Клиент пайдаланушы агентінен алынған деректер",
  ip4Client: "Клиенттік құрылғының IP мекенжайы",
  ip6Client: "Клиенттік құрылғының IPv6 мекенжайы",
  physicalAddressClient: "Клиенттік құрылғының физикалық мекенжайы",
  deviceImei: "Мобильді құрылғы идентификаторы (IMEI)",
  country: "Ел",
  region: "Аймақ / Облыс",
  city: "Қала",
  zip: "Почталық индекс",
  // Данные о пользователе
  iinbin: "ЖСН/БСН",
  userSystemId: "Жүйедегі қатысушы идентификаторы",
  isUserOrganizationClient: "Мүше ұйымның клиенті болып табылады",
  clientRequestProcessed: "Клиенттің қызмет көрсетуге қабылданған күні",
  clientTypeForm: "Клиент түрі (меншік нысаны)",
  clientRole: "Қатысушы рөлі",
  orgLegalForm: "Ұйымдастырушылық-құқықтық нысаны",
  name: "Аты",
  patronymic: "Әкесінің аты",
  lastname: "Тегі",
  shortname: "Қысқартылған аты",
  documentType: "Құжат түрі",
  documentNumber: "Құжат нөмірі",
  region: "Аймақ",
  district: "Аудан",
  cityandlivingplace: "Қала/елді мекен",
  streetandmicrodistrict: "Көше/Шағын аудан",
  homeandbuilding: "Үй / Ғимарат",
  apartmentandoffice: "Пәтер / Кеңсе",
  oldPostalIndex: "Ескі пошта индексі",
  postalIndex: "Пошталық индекс",
  codePKA: "RCA коды",
  phoneNumber: "Телефон нөмірі",
  birthdateAndRegistration: "Туған/тіркелген күні",
  citizenship: "Азаматтық",
  countryResidence: "Тұратын елі",
  countryBirthAndRegistration: "Туған/тіркелген елі",
  //  Данные события
  login: "Кіру",
  mobileNumber: "Мобильдік телефон номері",
  error: "Қате",
  // Данные об операций
  uniqueOperationId: "Операцияның бірегей идентификаторы",
  operationType: "Операция түрі",
  operationSubType: "Операцияның ішкі түрі",
  operationCreationDate: "Операцияның жасалған күні мен уақыты",
  knpCode: "КНП",
  kbkCode: "КБК",
  knoCode: "КНО",
  productGroupId: "Өнім тобының идентификаторы",
  productId: "Өнім идентификаторы",
  serviceId: "Қызмет идентификаторы",
  paymentAssignment: "Төлем мақсаты",
  openRefCode: "Операцияны жүргізетін бөлімнің коды",
  // Данные по кредитовой или дебетовой стороне операций
  currencyCode: "Үш таңбалы кодтағы валюта",
  currencyAmount: "Валютадағы сома",
  currencyAmountKzt: "Теңгедегі сома",
  debitCredit: "Дебет/Кредит",
  mcc: "MCC",
  cardType: "Карта түрі",
  cardOwner: "Карта меншік иесі",
  cardNumberMasked: "Маскаланған карта нөмірі",
  cardOpenDate: "Картаны ашу күні",
  cardExpDate: "Картаның жарамдылық мерзімі",
  bankBic: "Банктің БСК нөмірі",
  bankName: "Банктің атауы",
  gkAccount: "Бухгалтерлік есеп",
  serviceOperatorCode: "Қызмет көрсетуші коды",
  fiscalType: "Жеке шот түрі",
  fiscalNumber: "Шот нөмірі",
  // Настройки каналов интеграций
  integrationChannels: "Интеграциялық арналар",
  // Настройки статусов
  registration: "Оқиғалар саны",
  loginAuthorization: "Жүйеге кіру арқылы кіру",
  loginMobilePhone: "Ұялы телефонды авторизациялау",
  finOperation: "Қаржылық операция",
  profileChange: "Профильді өзгерту",
  minimalScore: "Ең төменгі ұпай",
  maximumScore: "Максималды ұпай",
  channel: "Арна",
  eventType: "Оқиға түрі",
  decision: "Шешім",
  // Настройки версий
  version: "Нұсқа",
  // Настройка уведомлений
  email: "Пошта",
  // Настройки хранения данных
  finOperationFull: "Қаржылық операциялар",
  userActions: "Пайдаланушы әрекеттері",
  registrationandauthorization: "Тіркелу / Кіру",
  // Списки DJ
  fio: "Толық аты",
  dictionary: "Анықтамалық",
  comment: "Пікір",
  birthdate: "Туған күні",
  occupationType: "Қызмет түрі",
  occupationStartDate: "Әрекеттің басталу күні",
  djId: "DJ идентификаторы",
  gender: "Еден",
  aliveOrDeceased: "Өлі /   Тірі",
  active: "Белсенд",
  updateDate: "Жаңарту күні",
  // Все правила
  scenarioName: "Сценарий аты",
  onOrOff: "Қосылған / Өшірілген",
  sendResultSysInit: "Нәтижені / және ішінен жіберу",
  shouldCreateCase: "Жағдайды жасау",
  changeScenario: "Скриптті өзгерту",
  // Правило 1
  order: "Тапсырыс",
  percentageMatch: "Сәйкестік пайызы",
  on: "Қосылған",
  editing: "Өңдеу",
  // Правило 2
  timeInterval: "Уақыт аралығы",
  eventsAmount: "Оқиғалар саны",
  eventsAmountShort: "Жылау саны",
  // Правило 3
  operationView: "Операция түрі",
  clientType: "Клиент түрі",
  sumLimit: "Сома шегі",
  scoreResult: "Ұпай көрсеткіші",
  operationLimit: "Транзакциялар санына шектеу",
  period: "Кезең",
  // timeOptions
  year: "Жыл",
  years: "Жыл",
  years2: "Жыл",
  day: "Күн",
  days: "Күн",
  days2: "Күн",
  month: "Ай",
  months: "Ай",
  months2: "Ай",
  hour: "Сағат",
  hours: "Сағат",
  hours2: "Сағат",
  minute: "Минут",
  minutes: "Минут",
  minutes2: "Минут",
  second: "Секунд",
  seconds: "Секунд",
  seconds2: "Секунд",
  // Правило 6
  sumThreshold: "Шекті сома",
  // Правило 7
  percent: "Пайыз",
  // Правило 8
  receiversCount: "Алушылардың саны",
  // Правило 9
  blackCardListSettings: "Қара тізім параметрлері",
  cardNumber: "Карта нөмірі",
  // Аналитика
  reportType: "Есеп түрі",
  datefrom: "Басталу күні",
  dateto: "Аяқтау күні",
  report1:
    "Оқиғалардың түрлері мен клиенттердің түрлері бойынша оқиғалар саны және істер саны",
  report2:
    "Елдер мен оқиғалар түрлері бойынша оқиғалар саны және істер саны",
  report3: "Арна бойынша оқиғалар саны және істер саны",
  report4: "Клиенттер мен елдер түрлері бойынша оқиғалар саны және істер саны",
  report5:
    "Клиент түрлері мен мәртебелері контекстіндегі «тіркеу» және «авторизация» оқиғалары бойынша істердің саны",
  report6:
    "Мәміле түрлері және клиент түрлері бойынша «қаржылық операция» оқиғасының сандық көрсеткіштері",
  report7:
    "Мәмілелер түрлері мен валюталар бойынша «қаржылық операция» оқиғасының сандық көрсеткіштері",
  report8:
    "Елдер мен мәміле түрлері бойынша «қаржылық операция» оқиғасының сандық көрсеткіштері",
  report9: "Оқиғалар мен күйлердің түрлері бойынша істер саны",
  report10: "Ескі алаяқтыққа қарсы істер",
  report11: "Бір карта бойынша жүргізілетін транзакциялардың жиілігі",
  report12: "Бір карта бойынша жүргізілетін операциялардың жиілігі #2",
  report13: "Барлық Транзакциялар",
  report14: "Үлкен сомалар",
  clientId: "Клиент идентификаторы",
  fullName: "Толық есім",
  caseCreationDate: "Істің жасалған күні",
  operationFrequency: "Операциялардың жиілігі",
  ourCardsAtUs: "Біздің карталарымыз өзімізде",
  ourCardsDifferentNetwork: "Басқа карталар басқа біреудің желісінде",
  bvuCardsOurs: "ЕДБ карталары бізде",
  downloadDate: "Жүктеу күні",
  userIdentification: "Пайдаланушының идентификаторы",
  sourceNumber: "Дереккөз нөмірі",
  authorizationCode: "Авторизация коды",
  transactionCountry: "Транзакцияны өткізген ел",
  transactionCity: "Транзакцияны өткізген қала",
  transactionDetails: "Транзакция мәліметтері",
  transactionDate: "Күні",
  transactionAmount: "Cомасы",
  transactionStatus: "Транзакция күйі",
  currency: "Валюта",
  merchantName: "Саудагер аты",
  caseCount: "Істер саны",
  caseCountShort: "Іст сан",
  authorization: "Iстер саны",
  finShortcaseCount: "Қарж",
  finOperShort: "Қаржы операция",
  finOper: "Қаржылық операция",
  // Профиль
  newPassword: "Жаңа құпия сөз",
  oldPassword: "Ескі құпия сөз",
  wordsInLowRegister: "Кіші әріптер",
  wordsInHigherRegister: "Бас әріптер",
  numbers: "Бөлмелер",
  min8Symbols: "Кемінде 8 таңба",
  requiredField: "Міндетті өріс",
  passwordMustContainAtLeast8Symbold:
    "Құпия сөз кемінде 8 таңбадан тұруы керек",
  role: "Рөл",
  update: "Жаңарту",
  // Пользователи
  changeUser: "Пайдаланушыны өзгерту",
  addNewUser: "Жаңа пайдаланушы қосу",
  addUser: "Пайдаланушы қосу",
  // Справочник ролей
  addNewRole: "Жаңа рөл қосыңыз",
  addRole: "Рөл қосу",
  roleNameRu: "Орыс тілінде рөл аты",
  roleNameEng: "Ағылшын тіліндегі рөл атауы",
  roleNameKz: "Қазақша рөл аты",
  changeRole: "Рөлді өзгерту",
  roles: "Рөлдері",
  // Клиенты белого списка
  clientswhitelist: "Ақ тізімге енгізілген клиенттер",
  changeClient: "Клиентті өзгерту",
  notes: "Ескерту",
  addClient: "Клиент қосу",
  // IP адреса из внешнего 'черного списка
  ipaddressexternalblacklist: "Сыртқы «қара тізімдегі» IP мекенжайлары",
  // IP адреса черный список
  ipadressblacklist: "Қызметкерлердің қара тізімі",
  changeIp: "IP өзгерту",
  ipAddress: "IP мекенжайы",
  addIp: "IP қосыңыз",
  // MAC адреса черный список
  macAddressesBlackList: "MAC мекенжайларының қара тізімі",
  changeMacAddress: "MAC өзгертіңіз",
  macAddress: "MAC мекенжайы",
  addMacAddress: "MAC қосыңыз",
  // Террористы талибан физ
  terroristTalibanIndv: "Талибан лаңкестері жеке тұлғалар",
  number: "Сан",
  firstName: "Аты",
  secondName: "Екінші аты",
  thirdName: "Үшінші аты",
  fourthName: "Төртінші есім",
  //  Террористы КФМ юр
  terroristKfmLe: "Террористер KFM заңды тұлғалары",
  orgNameEng: "Ұйымның ағылшын тіліндегі атауы",
  orgName: "Ұйым атауы",
  // Террористы КФМ физ
  terroristKfmIndv: "KFM террористері жеке тұлғалар",
  firstNameEng: "Ағылшын тіліндегі аты",
  lastnameEng: "Ағылшын тіліндегі фамилия",
  iin: "ЖСН",
  middleName: "Әкесінің аты",
  middleNameEng: "Ағылшын тіліндегі әкесінің аты",
  // Террористы SSI юр
  terroristSsiLe: "Террористер Ssi заңды тұлғалары",
  // Террористы Ssi физ
  terroristSsiIndv: "Террористер Ssi жеке тұлғалар",
  // Террористы ООН юр
  terroristOonLe: "Террористер ООН заңды тұлғалары",
  // Террористы ООН физ
  terroristOonIndv: "Террористер ООН жеке тұлғалар",
  // Террористы ОФАК юр
  terroristOfacLe: "Террористер ОФАК заңды тұлғалары",
  // Террористы ОФАК физ
  terroristOfacIndv: "Террористер ОФАК жеке тұлғалар",
  title: "Тақырып",
  // Террористы ЕС физ.лица
  terroristEsIndv: "Террористер ЕС жеке тұлғалар",
  fullNameEng: "Толық аты-жөні ағылшын тілінде",
  // Номер телефона черный список
  phoneNumbersBlackList: "Телефон нөмірі қара тізім",
  phone: "Телефон",
  addPhoneNumber: "Телефон нөмірін қосыңыз",
  editPhoneNumber: "Телефон нөмірін өзгерту",
  // Террористы талибан юр
  terroristTalibanLe: "Террористер талибан заңды тұлғалары",
  // Клиенты черного списка
  clientsBlackList: "Қара тізімге енгізілген клиенттер",

  //
  caseStatusesSettings: "Іс күйінің параметрлері",
  disabled: "Өшірілген",
  yes: "Ия",
  no: "Жоқ",

  // datemixin
  sunday: "Жексенбіде",
  sundayShort: "ЖС",
  monday: "Дүйсенбіде",
  mondayShort: "ДС",
  tuesday: "Сейсенбі күні",
  tuesdayShort: "СС",
  wednesday: "Сәрсенбіде",
  wednesdayShort: "СР",
  thursday: "Бейсенбіде",
  thursdayShort: "БС",
  friday: "Жұмада",
  fridayShort: "ЖМ",
  saturday: "Сенбі күні",
  saturdayShort: "СН",
  january: "Қаңтар",
  february: "Aқпан",
  march: "Наурыз",
  april: "Сәуір",
  may: "Мамыр",
  june: "Маусым",
  july: "Шілде",
  august: "Тамыз",
  september: "Қыркүйек",
  october: "Қазан",
  november: "Қараша",
  december: "Желтоқсан",
  janShort: "қаң",
  febShort: "ақп",
  marchShort: "нау",
  aprilShort: "сәу",
  mayShort: "мам",
  juneShort: "мау",
  julyShort: "шіл",
  augustShort: "там",
  septemberShort: "қыр",
  octoberShort: "қаз",
  novemberShort: "қар",
  decemberShort: "жел",

  // fl ul ip
  fl: "ЖТ",
  ul: "ЗТ ",
  ep: "ЖК",

  report2eventsAmount: "Оқиғалар саны",
  report2caseAmount: "Істер саны",


};
