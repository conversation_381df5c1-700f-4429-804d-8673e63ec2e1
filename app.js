var express = require('express')
var bodyParser = require('body-parser')
var path = require('path')

var app = express()
app.use(express.static('./dist/spa'))
app.use(bodyParser.urlencoded({ extended: false }))
app.get('/*', function (req, res) {
  res.sendFile(path.resolve(__dirname, './dist/spa/index.html'))
})
var port = 80
app.listen(port, () => {
  console.log(`Magic is happening on port ${port}`)
})
