(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[49],{

/***/ "./node_modules/@quasar/app/lib/webpack/loader.transform-quasar-imports.js!./node_modules/babel-loader/lib/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@quasar/app/lib/webpack/loader.transform-quasar-imports.js!./node_modules/babel-loader/lib??ref--2-0!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib??vue-loader-options!./src/pages/reportsall4onepage.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _Users_arsenzairov_Desktop_kazpost_front_node_modules_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/objectSpread2 */ \"./node_modules/@babel/runtime/helpers/objectSpread2.js\");\n/* harmony import */ var _Users_arsenzairov_Desktop_kazpost_front_node_modules_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Users_arsenzairov_Desktop_kazpost_front_node_modules_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var vuex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vuex */ \"./node_modules/vuex/dist/vuex.esm.js\");\n/* harmony import */ var _components_BarChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/BarChart */ \"./src/components/BarChart.vue\");\n/* harmony import */ var _mixins_datemixin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../mixins/datemixin */ \"./src/mixins/datemixin.js\");\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  mixins: [_mixins_datemixin__WEBPACK_IMPORTED_MODULE_3__[\"default\"]],\n  components: {\n    Bar: _components_BarChart__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n  },\n  data: function data() {\n    return {\n      report1options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          xAxes: [{\n            stacked: true,\n            categoryPercentage: 0.5,\n            barPercentage: 1\n          }],\n          yAxes: [{\n            stacked: true\n          }]\n        }\n      }\n    };\n  },\n  created: function created() {\n    var _this = this;\n\n    var start_date = new Date();\n    start_date.setHours(0, 0, 0, 0);\n    start_date = this.formatDateToDB(start_date);\n    var end_date = this.formatDateToDB(new Date());\n    this.getReport9({\n      start_date: start_date,\n      end_date: end_date\n    }).then(function () {\n      console.log(_this.report9);\n    }).catch(function (err) {\n      _this.$q.notify({\n        message: err.error,\n        color: \"negative\"\n      });\n    });\n  },\n  computed: Object(vuex__WEBPACK_IMPORTED_MODULE_1__[\"mapState\"])({\n    report9: function report9(state) {\n      return state.admin.report9;\n    },\n    report9datasets: function report9datasets() {\n      var report9labels = [\"Разрешено\", \"Приостановлено\", \"Запрещено\"];\n\n      if (this.report9) {\n        var report9 = this.report9;\n        var datasets = [{\n          label: \"Авторизация\",\n          backgroundColor: \"#f87979\",\n          pointBackgroundColor: \"white\",\n          borderWidth: 1,\n          pointBorderColor: \"#249EBF\",\n          data: [report9.auth_allowed, report9.auth_suspended, report9.auth_forbidden]\n        }, {\n          label: \"Регистрация\",\n          backgroundColor: \"#3D5B96\",\n          pointBackgroundColor: \"white\",\n          borderWidth: 1,\n          pointBorderColor: \"#249EBF\",\n          data: [report9.registration_allowed, report9.registration_suspended, report9.registration_forbidden]\n        }, {\n          label: \"Фин Операция\",\n          backgroundColor: \"pink\",\n          pointBackgroundColor: \"white\",\n          borderWidth: 1,\n          pointBorderColor: \"#249EBF\",\n          data: [report9.fin_oper_allowed, report9.fin_oper_suspended, report9.fin_oper_forbidden]\n        }];\n        return {\n          labels: report9labels,\n          datasets: datasets\n        };\n      }\n\n      return {\n        labels: report9labels,\n        datasets: []\n      };\n    }\n  }),\n  methods: _Users_arsenzairov_Desktop_kazpost_front_node_modules_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_0___default()({}, Object(vuex__WEBPACK_IMPORTED_MODULE_1__[\"mapActions\"])({\n    getReport9: \"admin/getReport9\"\n  }))\n});\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@quasar/app/lib/webpack/loader.transform-quasar-imports.js!./node_modules/babel-loader/lib/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&\n");

/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--6-oneOf-2-2!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib??vue-loader-options!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/api.js */ \"./node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(true);\n// Module\nexports.push([module.i, \"\\n.onerow {\\n  max-height: calc(200px - 100%);\\n}\\n.cardcontainer {\\n  border-radius: 16px;\\n  background: white;\\n  box-shadow: 0 5px 10px rgba(154, 160, 185, 0.05),\\n    0 15px 40px rgba(166, 173, 201, 0.2);\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"/Users/<USER>/Desktop/kazpost-front/src/pages/src/pages/reportsall4onepage.vue\"],\"names\":[],\"mappings\":\";AAiLA;EACA,8BAAA;AACA;AAEA;EACA,mBAAA;EACA,iBAAA;EACA;wCACA;AACA\",\"file\":\"reportsall4onepage.vue\",\"sourcesContent\":[\"<template>\\n  <q-page class=\\\"q-pa-lg column\\\">\\n    <div class=\\\"col column\\\" >\\n      <!-- <div class=\\\"row col q-pa-none q-pl-md q-pr-md justify-between\\\">\\n        <div\\n          class-name=\\\"cardcontainer\\\"\\n          :parent=\\\"true\\\"\\n          style=\\\"max-height: -webkit-fill-available; \\\"\\n        >\\n          <Bar\\n            :chartData=\\\"report9datasets\\\"\\n            :options=\\\"report1options\\\"\\n            :styles=\\\"{ height: '100%' }\\\"\\n          />\\n        </div>\\n        <div\\n          class-name=\\\"cardcontainer\\\"\\n          :parent=\\\"true\\\"\\n          style=\\\"max-height: -webkit-fill-available; \\\"\\n        >\\n          <Bar\\n            :chartData=\\\"report9datasets\\\"\\n            :options=\\\"report1options\\\"\\n            :styles=\\\"{ height: '100%' }\\\"\\n          />\\n        </div>\\n      </div> -->\\n\\n      <div\\n        class=\\\"row onerow q-pa-none q-pl-md q-pr-md justify-between \\\"\\n      >\\n        <div\\n          class=\\\"cardcontainer col\\\"\\n          style=\\\"max-height: -webkit-fill-available; \\\"\\n        >\\n          <Bar\\n            :chartData=\\\"report9datasets\\\"\\n            :options=\\\"report1options\\\"\\n            :styles=\\\"{ height: '100%' }\\\"\\n          />\\n        </div>\\n        <div\\n          class=\\\"cardcontainer col q-ml-md \\\"\\n          style=\\\"max-height: -webkit-fill-available; \\\"\\n        >\\n          <Bar\\n            :chartData=\\\"report9datasets\\\"\\n            :options=\\\"report1options\\\"\\n            :styles=\\\"{ height: '100%' }\\\"\\n          />\\n        </div>\\n      </div>\\n    </div>\\n  </q-page>\\n</template>\\n\\n<script>\\nimport { mapState, mapActions } from \\\"vuex\\\";\\nimport Bar from \\\"../components/BarChart\\\";\\nimport datemixin from \\\"../mixins/datemixin\\\";\\n\\nexport default {\\n  mixins: [datemixin],\\n  components: {\\n    Bar: Bar\\n  },\\n\\n  data() {\\n    return {\\n      report1options: {\\n        responsive: true,\\n        maintainAspectRatio: false,\\n        scales: {\\n          xAxes: [\\n            {\\n              stacked: true,\\n              categoryPercentage: 0.5,\\n              barPercentage: 1\\n            }\\n          ],\\n          yAxes: [\\n            {\\n              stacked: true\\n            }\\n          ]\\n        }\\n      }\\n    };\\n  },\\n\\n  created() {\\n    let start_date = new Date();\\n    start_date.setHours(0, 0, 0, 0);\\n    start_date = this.formatDateToDB(start_date);\\n    let end_date = this.formatDateToDB(new Date());\\n\\n    this.getReport9({ start_date, end_date })\\n      .then(() => {\\n        console.log(this.report9);\\n      })\\n      .catch(err => {\\n        this.$q.notify({\\n          message: err.error,\\n          color: \\\"negative\\\"\\n        });\\n      });\\n  },\\n  computed: mapState({\\n    report9: state => state.admin.report9,\\n    report9datasets() {\\n      const report9labels = [\\\"Разрешено\\\", \\\"Приостановлено\\\", \\\"Запрещено\\\"];\\n\\n      if (this.report9) {\\n        const report9 = this.report9;\\n\\n        const datasets = [\\n          {\\n            label: \\\"Авторизация\\\",\\n            backgroundColor: \\\"#f87979\\\",\\n            pointBackgroundColor: \\\"white\\\",\\n            borderWidth: 1,\\n            pointBorderColor: \\\"#249EBF\\\",\\n            data: [\\n              report9.auth_allowed,\\n              report9.auth_suspended,\\n              report9.auth_forbidden\\n            ]\\n          },\\n          {\\n            label: \\\"Регистрация\\\",\\n            backgroundColor: \\\"#3D5B96\\\",\\n            pointBackgroundColor: \\\"white\\\",\\n            borderWidth: 1,\\n            pointBorderColor: \\\"#249EBF\\\",\\n            data: [\\n              report9.registration_allowed,\\n              report9.registration_suspended,\\n              report9.registration_forbidden\\n            ]\\n          },\\n          {\\n            label: \\\"Фин Операция\\\",\\n            backgroundColor: \\\"pink\\\",\\n            pointBackgroundColor: \\\"white\\\",\\n            borderWidth: 1,\\n            pointBorderColor: \\\"#249EBF\\\",\\n            data: [\\n              report9.fin_oper_allowed,\\n              report9.fin_oper_suspended,\\n              report9.fin_oper_forbidden\\n            ]\\n          }\\n        ];\\n\\n        return {\\n          labels: report9labels,\\n          datasets\\n        };\\n      }\\n\\n      return {\\n        labels: report9labels,\\n        datasets: []\\n      };\\n    }\\n  }),\\n  methods: {\\n    ...mapActions({\\n      getReport9: \\\"admin/getReport9\\\"\\n    })\\n  }\\n};\\n</script>\\n\\n\\n<style >\\n\\n.onerow {\\n  max-height: calc(200px - 100%);  \\n}\\n\\n.cardcontainer {\\n  border-radius: 16px;\\n  background: white;\\n  box-shadow: 0 5px 10px rgba(154, 160, 185, 0.05),\\n    0 15px 40px rgba(166, 173, 201, 0.2);\\n}\\n</style>\"]}]);\n// Exports\nmodule.exports = exports;\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\n");

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib??vue-loader-options!./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return render; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return staticRenderFns; });\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"q-page\", { staticClass: \"q-pa-lg column\" }, [\n    _c(\"div\", { staticClass: \"col column\" }, [\n      _c(\n        \"div\",\n        {\n          staticClass: \"row onerow q-pa-none q-pl-md q-pr-md justify-between \"\n        },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"cardcontainer col\",\n              staticStyle: { \"max-height\": \"-webkit-fill-available\" }\n            },\n            [\n              _c(\"Bar\", {\n                attrs: {\n                  chartData: _vm.report9datasets,\n                  options: _vm.report1options,\n                  styles: { height: \"100%\" }\n                }\n              })\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"cardcontainer col q-ml-md \",\n              staticStyle: { \"max-height\": \"-webkit-fill-available\" }\n            },\n            [\n              _c(\"Bar\", {\n                attrs: {\n                  chartData: _vm.report9datasets,\n                  options: _vm.report1options,\n                  styles: { height: \"100%\" }\n                }\n              })\n            ],\n            1\n          )\n        ]\n      )\n    ])\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\n");

/***/ }),

/***/ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader??ref--6-oneOf-2-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--6-oneOf-2-2!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib??vue-loader-options!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = __webpack_require__(/*! !../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src??ref--6-oneOf-2-2!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=style&index=0&lang=css& */ \"./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.i, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = __webpack_require__(/*! ../../node_modules/vue-style-loader/lib/addStylesClient.js */ \"./node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"07222159\", content, false, {\"sourceMap\":true});\n// Hot Module Replacement\nif(true) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(/*! !../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src??ref--6-oneOf-2-2!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=style&index=0&lang=css& */ \"./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\", function() {\n     var newContent = __webpack_require__(/*! !../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src??ref--6-oneOf-2-2!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=style&index=0&lang=css& */ \"./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.i, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\n");

/***/ }),

/***/ "./src/pages/reportsall4onepage.vue":
/*!******************************************!*\
  !*** ./src/pages/reportsall4onepage.vue ***!
  \******************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reportsall4onepage.vue?vue&type=template&id=6243f8c7& */ \"./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\");\n/* harmony import */ var _reportsall4onepage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reportsall4onepage.vue?vue&type=script&lang=js& */ \"./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport *//* harmony import */ var _reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reportsall4onepage.vue?vue&type=style&index=0&lang=css& */ \"./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\");\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n/* harmony import */ var quasar_src_components_page_QPage_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! quasar/src/components/page/QPage.js */ \"./node_modules/quasar/src/components/page/QPage.js\");\n/* harmony import */ var _node_modules_quasar_app_lib_webpack_runtime_auto_import_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../node_modules/@quasar/app/lib/webpack/runtime.auto-import.js */ \"./node_modules/@quasar/app/lib/webpack/runtime.auto-import.js\");\n/* harmony import */ var _node_modules_quasar_app_lib_webpack_runtime_auto_import_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_quasar_app_lib_webpack_runtime_auto_import_js__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n  _reportsall4onepage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  null,\n  null\n  \n)\n\n\n\n\n_node_modules_quasar_app_lib_webpack_runtime_auto_import_js__WEBPACK_IMPORTED_MODULE_5___default()(component, 'components', {QPage: quasar_src_components_page_QPage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]});\n/* hot reload */\nif (true) {\n  var api = __webpack_require__(/*! ./node_modules/vue-hot-reload-api/dist/index.js */ \"./node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(__webpack_require__(/*! vue */ \"./node_modules/vue/dist/vue.runtime.esm.js\"))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6243f8c7')) {\n      api.createRecord('6243f8c7', component.options)\n    } else {\n      api.reload('6243f8c7', component.options)\n    }\n    module.hot.accept(/*! ./reportsall4onepage.vue?vue&type=template&id=6243f8c7& */ \"./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\", function(__WEBPACK_OUTDATED_DEPENDENCIES__) { /* harmony import */ _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reportsall4onepage.vue?vue&type=template&id=6243f8c7& */ \"./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\");\n(function () {\n      api.rerender('6243f8c7', {\n        render: _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n        staticRenderFns: _reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"]\n      })\n    })(__WEBPACK_OUTDATED_DEPENDENCIES__); }.bind(this))\n  }\n}\ncomponent.options.__file = \"src/pages/reportsall4onepage.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/reportsall4onepage.vue\n");

/***/ }),

/***/ "./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&":
/*!*******************************************************************!*\
  !*** ./src/pages/reportsall4onepage.vue?vue&type=script&lang=js& ***!
  \*******************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_quasar_app_lib_webpack_loader_transform_quasar_imports_js_node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../node_modules/@quasar/app/lib/webpack/loader.transform-quasar-imports.js!../../node_modules/babel-loader/lib??ref--2-0!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=script&lang=js& */ \"./node_modules/@quasar/app/lib/webpack/loader.transform-quasar-imports.js!./node_modules/babel-loader/lib/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__[\"default\"] = (_node_modules_quasar_app_lib_webpack_loader_transform_quasar_imports_js_node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[\"default\"]); \n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyYuanMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9zcmMvcGFnZXMvcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT85OTdiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb2QgZnJvbSBcIi0hLi4vLi4vbm9kZV9tb2R1bGVzL0BxdWFzYXIvYXBwL2xpYi93ZWJwYWNrL2xvYWRlci50cmFuc2Zvcm0tcXVhc2FyLWltcG9ydHMuanMhLi4vLi4vbm9kZV9tb2R1bGVzL2JhYmVsLWxvYWRlci9saWIvaW5kZXguanM/P3JlZi0tMi0wIS4uLy4uL25vZGVfbW9kdWxlcy9AcXVhc2FyL2FwcC9saWIvd2VicGFjay9sb2FkZXIuYXV0by1pbXBvcnQtY2xpZW50LmpzP2tlYmFiIS4uLy4uL25vZGVfbW9kdWxlcy92dWUtbG9hZGVyL2xpYi9pbmRleC5qcz8/dnVlLWxvYWRlci1vcHRpb25zIS4vcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyZcIjsgZXhwb3J0IGRlZmF1bHQgbW9kOyBleHBvcnQgKiBmcm9tIFwiLSEuLi8uLi9ub2RlX21vZHVsZXMvQHF1YXNhci9hcHAvbGliL3dlYnBhY2svbG9hZGVyLnRyYW5zZm9ybS1xdWFzYXItaW1wb3J0cy5qcyEuLi8uLi9ub2RlX21vZHVsZXMvYmFiZWwtbG9hZGVyL2xpYi9pbmRleC5qcz8/cmVmLS0yLTAhLi4vLi4vbm9kZV9tb2R1bGVzL0BxdWFzYXIvYXBwL2xpYi93ZWJwYWNrL2xvYWRlci5hdXRvLWltcG9ydC1jbGllbnQuanM/a2ViYWIhLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1sb2FkZXIvbGliL2luZGV4LmpzPz92dWUtbG9hZGVyLW9wdGlvbnMhLi9yZXBvcnRzYWxsNG9uZXBhZ2UudnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzJlwiIl0sIm1hcHBpbmdzIjoiQUFBQTtBQUFBO0FBQUEiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/reportsall4onepage.vue?vue&type=script&lang=js&\n");

/***/ }),

/***/ "./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&":
/*!***************************************************************************!*\
  !*** ./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css& ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_6_oneOf_2_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_2_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_2_2_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../node_modules/vue-style-loader??ref--6-oneOf-2-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-2-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src??ref--6-oneOf-2-2!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=style&index=0&lang=css& */ \"./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\");\n/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_6_oneOf_2_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_2_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_2_2_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_6_oneOf_2_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_2_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_2_2_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_6_oneOf_2_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_2_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_2_2_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if([\"default\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_6_oneOf_2_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_2_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_2_2_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT92dWUmdHlwZT1zdHlsZSZpbmRleD0wJmxhbmc9Y3NzJi5qcyIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL3NyYy9wYWdlcy9yZXBvcnRzYWxsNG9uZXBhZ2UudnVlP2EwZWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi0hLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1zdHlsZS1sb2FkZXIvaW5kZXguanM/P3JlZi0tNi1vbmVPZi0yLTAhLi4vLi4vbm9kZV9tb2R1bGVzL2Nzcy1sb2FkZXIvZGlzdC9janMuanM/P3JlZi0tNi1vbmVPZi0yLTEhLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1sb2FkZXIvbGliL2xvYWRlcnMvc3R5bGVQb3N0TG9hZGVyLmpzIS4uLy4uL25vZGVfbW9kdWxlcy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3JlZi0tNi1vbmVPZi0yLTIhLi4vLi4vbm9kZV9tb2R1bGVzL0BxdWFzYXIvYXBwL2xpYi93ZWJwYWNrL2xvYWRlci5hdXRvLWltcG9ydC1jbGllbnQuanM/a2ViYWIhLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1sb2FkZXIvbGliL2luZGV4LmpzPz92dWUtbG9hZGVyLW9wdGlvbnMhLi9yZXBvcnRzYWxsNG9uZXBhZ2UudnVlP3Z1ZSZ0eXBlPXN0eWxlJmluZGV4PTAmbGFuZz1jc3MmXCIiXSwibWFwcGluZ3MiOiJBQUFBO0FBQUE7QUFBQTtBQUFBOyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/reportsall4onepage.vue?vue&type=style&index=0&lang=css&\n");

/***/ }),

/***/ "./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&":
/*!*************************************************************************!*\
  !*** ./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7& ***!
  \*************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!../../node_modules/vue-loader/lib??vue-loader-options!./reportsall4onepage.vue?vue&type=template&id=6243f8c7& */ \"./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/@quasar/app/lib/webpack/loader.auto-import-client.js?kebab!./node_modules/vue-loader/lib/index.js?!./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\");\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"render\"]; });\n\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_quasar_app_lib_webpack_loader_auto_import_client_js_kebab_node_modules_vue_loader_lib_index_js_vue_loader_options_reportsall4onepage_vue_vue_type_template_id_6243f8c7___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"]; });\n\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD02MjQzZjhjNyYuanMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9zcmMvcGFnZXMvcmVwb3J0c2FsbDRvbmVwYWdlLnZ1ZT84YmY5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCItIS4uLy4uL25vZGVfbW9kdWxlcy92dWUtbG9hZGVyL2xpYi9sb2FkZXJzL3RlbXBsYXRlTG9hZGVyLmpzPz92dWUtbG9hZGVyLW9wdGlvbnMhLi4vLi4vbm9kZV9tb2R1bGVzL0BxdWFzYXIvYXBwL2xpYi93ZWJwYWNrL2xvYWRlci5hdXRvLWltcG9ydC1jbGllbnQuanM/a2ViYWIhLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1sb2FkZXIvbGliL2luZGV4LmpzPz92dWUtbG9hZGVyLW9wdGlvbnMhLi9yZXBvcnRzYWxsNG9uZXBhZ2UudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTYyNDNmOGM3JlwiIl0sIm1hcHBpbmdzIjoiQUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7Iiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/reportsall4onepage.vue?vue&type=template&id=6243f8c7&\n");

/***/ })

}]);