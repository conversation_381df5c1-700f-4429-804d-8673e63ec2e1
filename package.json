{"name": "crm_admin", "version": "0.0.1", "description": "A free and beautiful Quasar template for CRM.", "productName": "CRM Admin", "cordovaId": "org.cordova.quasar.app", "capacitorId": "", "author": "<PERSON><PERSON>", "private": true, "scripts": {"test": "echo \"No test specified\" && exit 0", "start": "quasar dev", "build": "export NODE_OPTIONS=--openssl-legacy-provider && quasar build"}, "dependencies": {"@quasar/extras": "^1.0.0", "axios": "^0.21.1", "chart.js": "^2.9.4", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "echarts": "^5.4.3", "quasar": "^1.0.0", "serialize-javascript": "^5.0.1", "vue-apexcharts": "^1.5.2", "vue-chartjs": "^3.5.1", "vue-draggable-resizable": "^2.3.0", "vue-echarts-v3": "^2.0.1", "vue-i18n": "^8.26.7", "vue-i18n-composable": "^0.2.2", "vuedraggable": "^2.23.2", "vuex-persist": "^3.1.3", "yargs-parser": "^20.2.0"}, "devDependencies": {"@quasar/app": "^2.0.0", "quasar-dotenv": "^1.0.5"}, "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}, "browserslist": ["last 1 version, not dead, ie >= 11"]}